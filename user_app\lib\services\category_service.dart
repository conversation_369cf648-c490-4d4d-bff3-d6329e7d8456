import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/category_model.dart';
import '../constants/app_constants.dart';

class CategoryService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'categories';

  /// Get all active categories
  static Future<List<CategoryModel>> getAllActiveCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching active categories: $e');
      return [];
    }
  }

  /// Get parent categories only
  static Future<List<CategoryModel>> getParentCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('parentCategoryId', isNull: true)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching parent categories: $e');
      return [];
    }
  }

  /// Get subcategories by parent ID
  static Future<List<CategoryModel>> getSubcategories(String parentCategoryId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('parentCategoryId', isEqualTo: parentCategoryId)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching subcategories: $e');
      return [];
    }
  }

  /// Get category by ID
  static Future<CategoryModel?> getCategoryById(String categoryId) async {
    try {
      final doc = await _firestore
          .collection(_collection)
          .doc(categoryId)
          .get();

      if (doc.exists) {
        return CategoryModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error fetching category by ID: $e');
      return null;
    }
  }

  /// Search categories
  static Future<List<CategoryModel>> searchCategories({
    required String query,
    int limit = 50,
  }) async {
    try {
      if (query.isEmpty) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .limit(limit * 2) // Get more to filter
          .get();

      final allCategories = querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();

      // Filter by search query
      final searchLower = query.toLowerCase();
      final filteredCategories = allCategories.where((category) {
        return category.name.toLowerCase().contains(searchLower) ||
               category.description.toLowerCase().contains(searchLower);
      }).toList();

      return filteredCategories.take(limit).toList();
    } catch (e) {
      print('Error searching categories: $e');
      return [];
    }
  }

  /// Get category names only (for backward compatibility)
  static Future<List<String>> getCategoryNames() async {
    try {
      final categories = await getAllActiveCategories();
      return categories.map((category) => category.name).toList();
    } catch (e) {
      print('Error fetching category names: $e');
      return [];
    }
  }

  /// Import categories from existing products (for migration)
  static Future<bool> importCategoriesFromProducts() async {
    try {
      // Get all unique categories from products
      final productsSnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .get();

      final Set<String> existingCategories = {};
      for (final doc in productsSnapshot.docs) {
        final data = doc.data();
        if (data['category'] != null) {
          existingCategories.add(data['category'].toString());
        }
      }

      // Check which categories already exist in categories collection
      final existingCategoriesSnapshot = await _firestore.collection(_collection).get();
      final Set<String> alreadyImported = {};
      for (final doc in existingCategoriesSnapshot.docs) {
        final data = doc.data();
        if (data['name'] != null) {
          alreadyImported.add(data['name'].toString());
        }
      }

      // Category icons mapping
      final Map<String, String> categoryIcons = {
        'Electronics': 'phone_android',
        'Fashion': 'checkroom',
        'Furniture': 'chair',
        'Sports': 'sports_soccer',
        'Books': 'book',
        'Beauty': 'face',
        'Automotive': 'directions_car',
        'Food & Beverages': 'restaurant',
        'Health & Wellness': 'health_and_safety',
        'Home & Garden': 'home',
        'Toys & Games': 'toys',
        'Other': 'category',
      };

      // Import new categories
      int importCount = 0;
      for (final categoryName in existingCategories) {
        if (!alreadyImported.contains(categoryName)) {
          final category = CategoryModel(
            id: _generateCategoryId(),
            name: categoryName,
            description: 'Imported from existing products',
            iconName: categoryIcons[categoryName] ?? 'category',
            isActive: true,
            sortOrder: importCount,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await _firestore
              .collection(_collection)
              .doc(category.id)
              .set(category.toMap());

          importCount++;
        }
      }

      print('Successfully imported $importCount categories');
      return true;
    } catch (e) {
      print('Error importing categories from products: $e');
      return false;
    }
  }

  /// Generate unique category ID
  static String _generateCategoryId() {
    return _firestore.collection(_collection).doc().id;
  }

  /// Test method to check Firestore connection
  static Future<void> testConnection() async {
    try {
      print('Testing Firestore connection...');
      final snapshot = await _firestore.collection(_collection).limit(1).get();
      print('Firestore connection successful. Found ${snapshot.docs.length} documents.');

      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        print('Sample document data: ${doc.data()}');
      }
    } catch (e) {
      print('Firestore connection failed: $e');
    }
  }
}
