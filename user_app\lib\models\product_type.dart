enum ProductType {
  regular('regular'),
  spareParts('spare_parts'),
  comingSoon('coming_soon');

  const ProductType(this.value);
  final String value;

  static ProductType fromString(String value) {
    switch (value) {
      case 'spare_parts':
        return ProductType.spareParts;
      case 'coming_soon':
        return ProductType.comingSoon;
      default:
        return ProductType.regular;
    }
  }

  String get displayName {
    switch (this) {
      case ProductType.spareParts:
        return 'Spare Parts';
      case ProductType.comingSoon:
        return 'Coming Soon';
      case ProductType.regular:
        return 'Regular';
    }
  }
}
