import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';
import '../../constants/app_constants.dart';

/// Reusable form field widget for product forms
class ProductFormField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final int maxLines;
  final bool enabled;
  final Widget? suffixIcon;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;

  const ProductFormField({
    super.key,
    required this.controller,
    required this.label,
    required this.hint,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.maxLines = 1,
    this.enabled = true,
    this.suffixIcon,
    this.inputFormatters,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enabled: enabled,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.errorColor),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
          ),
        ),
      ],
    );
  }
}

/// Dropdown field for categories
class ProductCategoryDropdown extends StatelessWidget {
  final String? selectedCategory;
  final List<String> categories;
  final void Function(String?) onChanged;
  final String? Function(String?)? validator;

  const ProductCategoryDropdown({
    super.key,
    required this.selectedCategory,
    required this.categories,
    required this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        DropdownButtonFormField<String>(
          value: selectedCategory,
          onChanged: onChanged,
          validator: validator,
          decoration: InputDecoration(
            hintText: 'Select category',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.primaryColor),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
          ),
          items: categories.map((category) {
            return DropdownMenuItem<String>(
              value: category,
              child: Text(category),
            );
          }).toList(),
        ),
      ],
    );
  }
}

/// Switch widget for boolean fields
class ProductSwitchField extends StatelessWidget {
  final String label;
  final bool value;
  final void Function(bool) onChanged;
  final String? subtitle;

  const ProductSwitchField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      child: SwitchListTile(
        title: Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        subtitle: subtitle != null ? Text(subtitle!) : null,
        value: value,
        onChanged: onChanged,
        activeColor: AppConstants.primaryColor,
      ),
    );
  }
}

/// Tags input widget
class ProductTagsInput extends StatefulWidget {
  final List<String> tags;
  final void Function(List<String>) onTagsChanged;

  const ProductTagsInput({
    super.key,
    required this.tags,
    required this.onTagsChanged,
  });

  @override
  State<ProductTagsInput> createState() => _ProductTagsInputState();
}

class _ProductTagsInputState extends State<ProductTagsInput> {
  final TextEditingController _tagController = TextEditingController();

  @override
  void dispose() {
    _tagController.dispose();
    super.dispose();
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !widget.tags.contains(tag)) {
      final newTags = [...widget.tags, tag];
      widget.onTagsChanged(newTags);
      _tagController.clear();
    }
  }

  void _removeTag(String tag) {
    final newTags = widget.tags.where((t) => t != tag).toList();
    widget.onTagsChanged(newTags);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tags',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _tagController,
                decoration: InputDecoration(
                  hintText: 'Enter tag and press add',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
                ),
                onFieldSubmitted: (_) => _addTag(),
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            ElevatedButton(
              onPressed: _addTag,
              child: const Text('Add'),
            ),
          ],
        ),
        if (widget.tags.isNotEmpty) ...[
          const SizedBox(height: AppConstants.paddingSmall),
          Wrap(
            spacing: AppConstants.paddingSmall,
            runSpacing: AppConstants.paddingSmall,
            children: widget.tags.map((tag) {
              return Chip(
                label: Text(tag),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeTag(tag),
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}

/// Image picker widget for product images
class ProductImagePicker extends StatefulWidget {
  final List<XFile> selectedImages;
  final List<String> imageUrls;
  final void Function() onPickImages;
  final void Function(int) onRemoveImage;
  final void Function(int) onRemoveImageUrl;
  final void Function(String) onAddImageUrl;
  final int maxImages;

  const ProductImagePicker({
    super.key,
    required this.selectedImages,
    required this.imageUrls,
    required this.onPickImages,
    required this.onRemoveImage,
    required this.onRemoveImageUrl,
    required this.onAddImageUrl,
    this.maxImages = 10,
  });

  @override
  State<ProductImagePicker> createState() => _ProductImagePickerState();
}

class _ProductImagePickerState extends State<ProductImagePicker> {
  final TextEditingController _urlController = TextEditingController();

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final totalImages = widget.selectedImages.length + widget.imageUrls.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Product Images',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
            Text(
              '$totalImages/${widget.maxImages}',
              style: const TextStyle(
                color: AppConstants.textSecondaryColor,
                fontSize: AppConstants.fontSizeSmall,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),

        // Image URL input section
        _buildImageUrlInput(),
        const SizedBox(height: AppConstants.paddingMedium),

        Container(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: totalImages + (totalImages < widget.maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == totalImages) {
                // Add image button
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: InkWell(
                    onTap: widget.onPickImages,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppConstants.borderColor,
                          style: BorderStyle.solid,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_photo_alternate,
                            size: 32,
                            color: AppConstants.textSecondaryColor,
                          ),
                          SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            'Add Image',
                            style: TextStyle(
                              color: AppConstants.textSecondaryColor,
                              fontSize: AppConstants.fontSizeSmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Check if this is a file image or URL image
              if (index < widget.selectedImages.length) {
                // File image
                return _buildFileImageItem(index);
              } else {
                // URL image
                final urlIndex = index - widget.selectedImages.length;
                return _buildUrlImageItem(urlIndex);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImageUrlInput() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.borderColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        color: AppConstants.surfaceColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Add Image URL',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _urlController,
                  decoration: const InputDecoration(
                    hintText: 'Enter image URL (https://...)',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                  ),
                  keyboardType: TextInputType.url,
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              ElevatedButton(
                onPressed: _addImageUrl,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                ),
                child: const Text('Add'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addImageUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty && _isValidImageUrl(url)) {
      final totalImages = widget.selectedImages.length + widget.imageUrls.length;
      if (totalImages < widget.maxImages) {
        widget.onAddImageUrl(url);
        _urlController.clear();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Maximum ${widget.maxImages} images allowed'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid image URL'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  bool _isValidImageUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
             (uri.scheme == 'http' || uri.scheme == 'https') &&
             (url.toLowerCase().endsWith('.jpg') ||
              url.toLowerCase().endsWith('.jpeg') ||
              url.toLowerCase().endsWith('.png') ||
              url.toLowerCase().endsWith('.gif') ||
              url.toLowerCase().endsWith('.webp'));
    } catch (e) {
      return false;
    }
  }

  Widget _buildFileImageItem(int index) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            child: kIsWeb
                ? Image.network(
                    widget.selectedImages[index].path,
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 120,
                        height: 120,
                        color: AppConstants.backgroundColor,
                        child: const Icon(
                          Icons.error,
                          color: AppConstants.errorColor,
                        ),
                      );
                    },
                  )
                : Image.file(
                    File(widget.selectedImages[index].path),
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 120,
                        height: 120,
                        color: AppConstants.backgroundColor,
                        child: const Icon(
                          Icons.error,
                          color: AppConstants.errorColor,
                        ),
                      );
                    },
                  ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: InkWell(
              onTap: () => widget.onRemoveImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: AppConstants.errorColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUrlImageItem(int index) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            child: Image.network(
              widget.imageUrls[index],
              width: 120,
              height: 120,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  width: 120,
                  height: 120,
                  color: AppConstants.backgroundColor,
                  child: const Center(child: CircularProgressIndicator()),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 120,
                  height: 120,
                  color: AppConstants.backgroundColor,
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: AppConstants.errorColor,
                        size: 24,
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Failed to load',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.errorColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: InkWell(
              onTap: () => widget.onRemoveImageUrl(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: AppConstants.errorColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Image picker widget for editing products (handles both existing and new images)
class EditProductImagePicker extends StatefulWidget {
  final List<String> existingImages;
  final List<XFile> newImages;
  final List<String> imageUrls;
  final void Function() onPickImages;
  final void Function(int) onRemoveNewImage;
  final void Function(int) onRemoveExistingImage;
  final void Function(int) onRemoveImageUrl;
  final void Function(String) onAddImageUrl;
  final int maxImages;

  const EditProductImagePicker({
    super.key,
    required this.existingImages,
    required this.newImages,
    required this.imageUrls,
    required this.onPickImages,
    required this.onRemoveNewImage,
    required this.onRemoveExistingImage,
    required this.onRemoveImageUrl,
    required this.onAddImageUrl,
    this.maxImages = 10,
  });

  @override
  State<EditProductImagePicker> createState() => _EditProductImagePickerState();
}

class _EditProductImagePickerState extends State<EditProductImagePicker> {
  final TextEditingController _urlController = TextEditingController();

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final totalImages = widget.existingImages.length + widget.newImages.length + widget.imageUrls.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Product Images',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
            Text(
              '$totalImages/${widget.maxImages}',
              style: const TextStyle(
                color: AppConstants.textSecondaryColor,
                fontSize: AppConstants.fontSizeSmall,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),

        // Image URL input section
        _buildImageUrlInput(),
        const SizedBox(height: AppConstants.paddingMedium),

        Container(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: totalImages + (totalImages < widget.maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == totalImages) {
                // Add image button
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: InkWell(
                    onTap: widget.onPickImages,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppConstants.borderColor,
                          style: BorderStyle.solid,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_photo_alternate,
                            size: 32,
                            color: AppConstants.textSecondaryColor,
                          ),
                          SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            'Add Image',
                            style: TextStyle(
                              color: AppConstants.textSecondaryColor,
                              fontSize: AppConstants.fontSizeSmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Determine if this is an existing, new, or URL image
              if (index < widget.existingImages.length) {
                // Existing image
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        child: CachedNetworkImage(
                          imageUrl: widget.existingImages[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 120,
                            height: 120,
                            color: AppConstants.backgroundColor,
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 120,
                            height: 120,
                            color: AppConstants.backgroundColor,
                            child: const Icon(
                              Icons.error,
                              color: AppConstants.errorColor,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: InkWell(
                          onTap: () => widget.onRemoveExistingImage(index),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: AppConstants.errorColor,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else if (index < widget.existingImages.length + widget.newImages.length) {
                // New image
                final newImageIndex = index - widget.existingImages.length;
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        child: kIsWeb
                            ? Image.network(
                                widget.newImages[newImageIndex].path,
                                width: 120,
                                height: 120,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 120,
                                    height: 120,
                                    color: AppConstants.backgroundColor,
                                    child: const Icon(
                                      Icons.error,
                                      color: AppConstants.errorColor,
                                    ),
                                  );
                                },
                              )
                            : Image.file(
                                File(widget.newImages[newImageIndex].path),
                                width: 120,
                                height: 120,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 120,
                                    height: 120,
                                    color: AppConstants.backgroundColor,
                                    child: const Icon(
                                      Icons.error,
                                      color: AppConstants.errorColor,
                                    ),
                                  );
                                },
                              ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: InkWell(
                          onTap: () => widget.onRemoveNewImage(newImageIndex),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: AppConstants.errorColor,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      // Badge to indicate new image
                      Positioned(
                        top: 4,
                        left: 4,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppConstants.successColor,
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: const Text(
                            'NEW',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                // URL image
                final urlIndex = index - widget.existingImages.length - widget.newImages.length;
                return _buildUrlImageItem(urlIndex);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImageUrlInput() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.borderColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        color: AppConstants.surfaceColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Add Image URL',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _urlController,
                  decoration: const InputDecoration(
                    hintText: 'Enter image URL (https://...)',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                  ),
                  keyboardType: TextInputType.url,
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              ElevatedButton(
                onPressed: _addImageUrl,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                ),
                child: const Text('Add'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addImageUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty && _isValidImageUrl(url)) {
      final totalImages = widget.existingImages.length + widget.newImages.length + widget.imageUrls.length;
      if (totalImages < widget.maxImages) {
        widget.onAddImageUrl(url);
        _urlController.clear();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Maximum ${widget.maxImages} images allowed'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid image URL'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  bool _isValidImageUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
             (uri.scheme == 'http' || uri.scheme == 'https') &&
             (url.toLowerCase().endsWith('.jpg') ||
              url.toLowerCase().endsWith('.jpeg') ||
              url.toLowerCase().endsWith('.png') ||
              url.toLowerCase().endsWith('.gif') ||
              url.toLowerCase().endsWith('.webp'));
    } catch (e) {
      return false;
    }
  }

  Widget _buildUrlImageItem(int index) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            child: Image.network(
              widget.imageUrls[index],
              width: 120,
              height: 120,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  width: 120,
                  height: 120,
                  color: AppConstants.backgroundColor,
                  child: const Center(child: CircularProgressIndicator()),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 120,
                  height: 120,
                  color: AppConstants.backgroundColor,
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: AppConstants.errorColor,
                        size: 24,
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Failed to load',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.errorColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: InkWell(
              onTap: () => widget.onRemoveImageUrl(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: AppConstants.errorColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          // Badge to indicate URL image
          Positioned(
            top: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              child: const Text(
                'URL',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
