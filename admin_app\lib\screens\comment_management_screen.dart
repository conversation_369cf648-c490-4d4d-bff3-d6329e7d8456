import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../models/comment_model.dart';
import '../services/comment_service.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/error_widget.dart';

class CommentManagementScreen extends StatefulWidget {
  const CommentManagementScreen({super.key});

  @override
  State<CommentManagementScreen> createState() => _CommentManagementScreenState();
}

class _CommentManagementScreenState extends State<CommentManagementScreen> {
  bool _isLoading = false;
  List<CommentModel> _allComments = [];

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  Future<void> _loadComments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final comments = await CommentService.getAllCommentsPaginated(limit: 100);
      // Filter out deleted comments
      final activeComments = comments.where((comment) => !comment.isDeleted && comment.isActive).toList();
      setState(() {
        _allComments = activeComments;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading comments: ${e.toString()}'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteComment(String commentId) async {
    final confirmed = await _showDeleteConfirmation();
    if (!confirmed) return;

    try {
      final success = await CommentService.deleteComment(commentId);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment deleted successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        _loadComments();
      } else {
        throw Exception('Failed to delete comment');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting comment: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  Future<void> _approveComment(String commentId) async {
    try {
      final success = await CommentService.updateCommentStatus(
        commentId: commentId,
        isApproved: true,
        isActive: true,
      );
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment approved successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        _loadComments();
      } else {
        throw Exception('Failed to approve comment');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error approving comment: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  Future<void> _rejectComment(String commentId) async {
    try {
      final success = await CommentService.updateCommentStatus(
        commentId: commentId,
        isApproved: false,
        isActive: false,
        moderatorNote: 'Rejected by admin',
      );
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment rejected successfully'),
            backgroundColor: AppConstants.warningColor,
          ),
        );
        _loadComments();
      } else {
        throw Exception('Failed to reject comment');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error rejecting comment: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Comment'),
        content: const Text('Are you sure you want to delete this comment? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppConstants.errorColor),
            child: const Text('Delete'),
          ),
        ],
      ),
    ) ?? false;
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppConstants.backgroundColor,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildCommentsList(_allComments, 'all'),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.comment_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Comment Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _loadComments,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }



  Widget _buildCommentsList(List<CommentModel> comments, String type) {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (comments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.comment_outlined,
              size: 64,
              color: AppConstants.textSecondaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No comments found',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: comments.length,
      itemBuilder: (context, index) {
        final comment = comments[index];
        return _buildCommentCard(comment, type);
      },
    );
  }

  Widget _buildCommentCard(CommentModel comment, String type) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info and comment metadata
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundImage: comment.userProfileImageUrl != null
                      ? NetworkImage(comment.userProfileImageUrl!)
                      : null,
                  child: comment.userProfileImageUrl == null
                      ? Text(
                          comment.userDisplayName.isNotEmpty
                              ? comment.userDisplayName[0].toUpperCase()
                              : 'U',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        comment.userDisplayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                      Text(
                        '@${comment.username}',
                        style: TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      DateFormat('MMM dd, yyyy').format(comment.createdAt),
                      style: TextStyle(
                        color: AppConstants.textSecondaryColor,
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                    ),
                    Text(
                      DateFormat('HH:mm').format(comment.createdAt),
                      style: TextStyle(
                        color: AppConstants.textSecondaryColor,
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingSmall),

            // Comment content
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(color: AppConstants.borderColor),
              ),
              child: Text(
                comment.content,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  height: 1.4,
                ),
              ),
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            // Comment stats and status
            Row(
              children: [
                Icon(
                  Icons.favorite_outline,
                  size: 14,
                  color: AppConstants.textSecondaryColor,
                ),
                const SizedBox(width: 4),
                Text(
                  '${comment.likes.length}',
                  style: TextStyle(
                    color: AppConstants.textSecondaryColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Text(
                  'Post ID: ${comment.postId.substring(0, 8)}...',
                  style: TextStyle(
                    color: AppConstants.textSecondaryColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
                const Spacer(),


              ],
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (!comment.isApproved) ...[
                  TextButton.icon(
                    onPressed: () => _approveComment(comment.id),
                    icon: const Icon(Icons.check, size: 14),
                    label: const Text('Approve'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppConstants.successColor,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  TextButton.icon(
                    onPressed: () => _rejectComment(comment.id),
                    icon: const Icon(Icons.close, size: 14),
                    label: const Text('Reject'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppConstants.errorColor,
                    ),
                  ),
                ] else ...[
                  TextButton.icon(
                    onPressed: () => _viewCommentDetails(comment),
                    icon: const Icon(Icons.visibility, size: 14),
                    label: const Text('View'),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  TextButton.icon(
                    onPressed: () => _deleteComment(comment.id),
                    icon: const Icon(Icons.delete, size: 14),
                    label: const Text('Delete'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppConstants.errorColor,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(CommentModel comment) {
    if (!comment.isActive || comment.isDeleted) {
      return AppConstants.errorColor;
    } else if (!comment.isApproved) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.successColor;
    }
  }

  String _getStatusText(CommentModel comment) {
    if (!comment.isActive || comment.isDeleted) {
      return 'Deleted';
    } else if (!comment.isApproved) {
      return 'Pending';
    } else {
      return 'Active';
    }
  }

  void _viewCommentDetails(CommentModel comment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Comment by ${comment.userDisplayName}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Username: @${comment.username}'),
              const SizedBox(height: 8),
              Text('Post ID: ${comment.postId}'),
              const SizedBox(height: 8),
              Text('Created: ${DateFormat('MMM dd, yyyy HH:mm').format(comment.createdAt)}'),
              const SizedBox(height: 8),
              Text('Updated: ${DateFormat('MMM dd, yyyy HH:mm').format(comment.updatedAt)}'),
              const SizedBox(height: 16),
              const Text('Content:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppConstants.backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppConstants.borderColor),
                ),
                child: Text(comment.content),
              ),
              const SizedBox(height: 16),
              Text('Likes: ${comment.likes.length}'),
              Text('Status: ${_getStatusText(comment)}'),
              if (comment.moderatorNote != null) ...[
                const SizedBox(height: 8),
                Text('Moderator Note: ${comment.moderatorNote}'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
