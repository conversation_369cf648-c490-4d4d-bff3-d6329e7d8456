import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'cloudinary_service.dart';

class ImageService {
  static const int _maxImageSize = 2 * 1024 * 1024; // 2MB
  static const int _maxImageWidth = 1920;
  static const int _maxImageHeight = 1920;
  static const int _compressionQuality = 85;

  /// Prepare image for upload by compressing and resizing
  static Future<dynamic> _prepareImageForUpload(XFile imageFile) async {
    try {
      if (kIsWeb) {
        // For web platform
        final bytes = await imageFile.readAsBytes();
        
        // Check file size
        if (bytes.length > _maxImageSize) {
          // Compress image
          final compressedBytes = await _compressImageBytes(bytes);
          return compressedBytes;
        }
        
        return bytes;
      } else {
        // For mobile platforms
        final file = File(imageFile.path);
        final fileSize = await file.length();
        
        if (fileSize > _maxImageSize) {
          // Compress image
          final bytes = await file.readAsBytes();
          final compressedBytes = await _compressImageBytes(bytes);
          
          // Create temporary file with compressed data
          final tempDir = Directory.systemTemp;
          final tempFile = File('${tempDir.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg');
          await tempFile.writeAsBytes(compressedBytes);
          
          return tempFile;
        }
        
        return file;
      }
    } catch (e) {
      print('Error preparing image for upload: $e');
      rethrow;
    }
  }

  /// Compress image bytes
  static Future<Uint8List> _compressImageBytes(Uint8List bytes) async {
    try {
      // Decode image
      final image = img.decodeImage(bytes);
      if (image == null) throw Exception('Failed to decode image');

      // Resize if necessary
      img.Image resizedImage = image;
      if (image.width > _maxImageWidth || image.height > _maxImageHeight) {
        resizedImage = img.copyResize(
          image,
          width: image.width > image.height ? _maxImageWidth : null,
          height: image.height > image.width ? _maxImageHeight : null,
        );
      }

      // Encode as JPEG with compression
      final compressedBytes = img.encodeJpg(resizedImage, quality: _compressionQuality);
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      print('Error compressing image: $e');
      rethrow;
    }
  }

  /// Pick single image
  static Future<XFile?> pickSingleImage({
    ImageSource source = ImageSource.gallery,
    int imageQuality = 85,
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: imageQuality,
        maxWidth: _maxImageWidth.toDouble(),
        maxHeight: _maxImageHeight.toDouble(),
      );
      
      return image;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }

  /// Pick multiple images
  static Future<List<XFile>> pickMultipleImages({
    int maxImages = 10,
    int imageQuality = 85,
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: imageQuality,
        maxWidth: _maxImageWidth.toDouble(),
        maxHeight: _maxImageHeight.toDouble(),
      );
      
      // Limit number of images
      if (images.length > maxImages) {
        return images.take(maxImages).toList();
      }
      
      return images;
    } catch (e) {
      print('Error picking multiple images: $e');
      return [];
    }
  }

  /// Upload single image
  static Future<String?> uploadSingleImage({
    required XFile imageFile,
    required String folder,
    String? publicId,
    int quality = 85,
    int? width,
    int? height,
  }) async {
    try {
      final preparedImage = await _prepareImageForUpload(imageFile);
      
      return await CloudinaryService.uploadImage(
        imageFile: preparedImage,
        folder: folder,
        publicId: publicId,
        quality: quality,
        width: width,
        height: height,
      );
    } catch (e) {
      print('Error uploading single image: $e');
      return null;
    }
  }

  /// Upload multiple images
  static Future<List<String>> uploadMultipleImages({
    required List<XFile> imageFiles,
    required String folder,
    String? basePublicId,
    int quality = 85,
    int? width,
    int? height,
  }) async {
    try {
      List<dynamic> preparedImages = [];
      for (XFile imageFile in imageFiles) {
        final preparedImage = await _prepareImageForUpload(imageFile);
        preparedImages.add(preparedImage);
      }

      return await CloudinaryService.uploadMultipleImages(
        imageFiles: preparedImages,
        folder: folder,
        basePublicId: basePublicId ?? 'image_${DateTime.now().millisecondsSinceEpoch}',
        quality: quality,
        width: width,
        height: height,
      );
    } catch (e) {
      print('Error uploading multiple images: $e');
      return [];
    }
  }

  /// Upload product images
  static Future<List<String>> uploadProductImages({
    required List<XFile> imageFiles,
    required String productId,
  }) async {
    try {
      List<dynamic> preparedImages = [];
      for (XFile imageFile in imageFiles) {
        final preparedImage = await _prepareImageForUpload(imageFile);
        preparedImages.add(preparedImage);
      }

      return await CloudinaryService.uploadMultipleImages(
        imageFiles: preparedImages,
        folder: 'product_images',
        basePublicId: 'product_$productId',
        quality: 85,
        width: 800,
        height: 800,
      );
    } catch (e) {
      print('Error uploading product images: $e');
      return [];
    }
  }

  /// Upload profile image
  static Future<String?> uploadProfileImage({
    required XFile imageFile,
    required String userId,
  }) async {
    try {
      final preparedImage = await _prepareImageForUpload(imageFile);

      return await CloudinaryService.uploadImage(
        imageFile: preparedImage,
        folder: 'profile_images',
        publicId: 'profile_$userId',
        quality: 90,
        width: 400,
        height: 400,
      );
    } catch (e) {
      print('Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload category icon
  static Future<String?> uploadCategoryIcon({
    required XFile imageFile,
    required String categoryId,
  }) async {
    try {
      final preparedImage = await _prepareImageForUpload(imageFile);

      return await CloudinaryService.uploadCategoryIcon(
        imageFile: preparedImage,
        categoryId: categoryId,
      );
    } catch (e) {
      print('Error uploading category icon: $e');
      return null;
    }
  }

  /// Delete image from Cloudinary
  static Future<bool> deleteImage(String publicId) async {
    try {
      return await CloudinaryService.deleteImage(publicId);
    } catch (e) {
      print('Error deleting image: $e');
      return false;
    }
  }

  /// Get optimized image URL
  static String getOptimizedImageUrl({
    required String imageUrl,
    int? width,
    int? height,
    int quality = 80,
    String format = 'auto',
  }) {
    return CloudinaryService.getOptimizedImageUrl(
      imageUrl: imageUrl,
      width: width,
      height: height,
      quality: quality,
      format: format,
    );
  }

  /// Validate image file
  static bool isValidImageFile(XFile file) {
    final allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    final extension = file.path.split('.').last.toLowerCase();
    return allowedExtensions.contains(extension);
  }

  /// Get image file size in MB
  static Future<double> getImageSizeInMB(XFile file) async {
    try {
      if (kIsWeb) {
        final bytes = await file.readAsBytes();
        return bytes.length / (1024 * 1024);
      } else {
        final fileSize = await File(file.path).length();
        return fileSize / (1024 * 1024);
      }
    } catch (e) {
      print('Error getting image size: $e');
      return 0.0;
    }
  }
}
