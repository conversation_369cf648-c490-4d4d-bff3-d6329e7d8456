import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import '../../constants/app_constants.dart';
import '../../models/slider_model.dart';
import '../../services/image_service.dart';

class SliderFormDialog extends StatefulWidget {
  final SliderModel? slider;

  const SliderFormDialog({super.key, this.slider});

  @override
  State<SliderFormDialog> createState() => _SliderFormDialogState();
}

class _SliderFormDialogState extends State<SliderFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _linkUrlController = TextEditingController();
  final _imageUrlController = TextEditingController();

  bool _isActive = true;
  bool _isLoading = false;

  // Image handling
  XFile? _selectedImage;
  Uint8List? _webImage;
  String? _existingImageUrl;
  bool _useUrlImage = false;

  @override
  void initState() {
    super.initState();
    if (widget.slider != null) {
      _titleController.text = widget.slider!.title;
      _descriptionController.text = widget.slider!.description ?? '';
      _linkUrlController.text = widget.slider!.linkUrl ?? '';
      _isActive = widget.slider!.isActive;
      _existingImageUrl = widget.slider!.imageUrl;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _linkUrlController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1920,
      maxHeight: 1080,
      imageQuality: 85,
    );

    if (image != null) {
      setState(() {
        _selectedImage = image;
      });

      if (kIsWeb) {
        final bytes = await image.readAsBytes();
        setState(() {
          _webImage = bytes;
        });
      }
    }
  }

  Future<void> _saveSlider() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      String imageUrl = _existingImageUrl ?? '';

      // Check if using URL image
      if (_useUrlImage && _imageUrlController.text.trim().isNotEmpty) {
        imageUrl = _imageUrlController.text.trim();
      }
      // Upload new image if selected
      else if (_selectedImage != null) {
        final uploadedUrl = await ImageService.uploadSingleImage(
          imageFile: _selectedImage!,
          folder: 'sliders',
          publicId: 'slider_${DateTime.now().millisecondsSinceEpoch}',
          quality: 85,
          width: 1920,
          height: 1080,
        );

        if (uploadedUrl != null) {
          imageUrl = uploadedUrl;
        } else {
          throw Exception('Failed to upload image');
        }
      }

      if (imageUrl.isEmpty) {
        throw Exception('Image is required');
      }

      final slider = SliderModel(
        id: widget.slider?.id ?? '',
        imageUrl: imageUrl,
        title: _titleController.text.trim().isEmpty
            ? ''
            : _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        linkUrl: _linkUrlController.text.trim().isEmpty 
            ? null 
            : _linkUrlController.text.trim(),
        isActive: _isActive,
        order: widget.slider?.order ?? 0,
        createdAt: widget.slider?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      Navigator.of(context).pop(slider);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 600,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9 > 600
              ? 600
              : MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      Icons.slideshow,
                      color: AppConstants.primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Text(
                        widget.slider == null ? 'Add Slider' : 'Edit Slider',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Image Section
                _buildImageSection(),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Form Fields
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title (Optional)',
                    hintText: 'Enter slider title (optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.title),
                  ),
                  maxLength: 100,
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (Optional)',
                    hintText: 'Enter slider description (optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 3,
                  maxLength: 500,
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                TextFormField(
                  controller: _linkUrlController,
                  decoration: const InputDecoration(
                    labelText: 'Link URL (Optional)',
                    hintText: 'Enter link URL (optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.link),
                  ),
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      final uri = Uri.tryParse(value.trim());
                      if (uri == null || !uri.hasScheme || (!uri.scheme.startsWith('http'))) {
                        return 'Please enter a valid URL';
                      }
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Active Switch
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.visibility, color: AppConstants.primaryColor),
                      const SizedBox(width: AppConstants.paddingSmall),
                      const Text(
                        'Active Status',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      const Spacer(),
                      Switch(
                        value: _isActive,
                        activeColor: AppConstants.primaryColor,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Action Buttons
                Container(
                  padding: const EdgeInsets.only(top: AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        label: const Text('Cancel'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      ElevatedButton.icon(
                        onPressed: _isLoading ? null : _saveSlider,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Icon(widget.slider == null ? Icons.add : Icons.update),
                        label: Text(widget.slider == null ? 'Add Slider' : 'Update Slider'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingLarge,
                            vertical: AppConstants.paddingMedium,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.image, color: AppConstants.primaryColor),
            const SizedBox(width: AppConstants.paddingSmall),
            const Text(
              'Slider Image',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Text(
              ' *',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Upload an image or provide a URL. Recommended size: 1920x1080px',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),

        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          child: _buildImageContent(),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        // Image upload options
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _useUrlImage ? null : _pickImage,
                icon: const Icon(Icons.image),
                label: Text(_selectedImage != null || _existingImageUrl != null
                    ? 'Change Image'
                    : 'Select Image'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _useUrlImage ? Colors.grey : null,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _useUrlImage = !_useUrlImage;
                    if (_useUrlImage) {
                      _selectedImage = null;
                      _webImage = null;
                    } else {
                      _imageUrlController.clear();
                    }
                  });
                },
                icon: Icon(_useUrlImage ? Icons.file_upload : Icons.link),
                label: Text(_useUrlImage ? 'Use File' : 'Use URL'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _useUrlImage ? AppConstants.primaryColor : Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),

        // URL input field when URL option is selected
        if (_useUrlImage) ...[
          const SizedBox(height: AppConstants.paddingMedium),
          TextFormField(
            controller: _imageUrlController,
            decoration: const InputDecoration(
              labelText: 'Image URL',
              hintText: 'Enter image URL (https://...)',
              prefixIcon: Icon(Icons.link),
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (_useUrlImage && (value == null || value.trim().isEmpty)) {
                return 'Please enter an image URL';
              }
              if (_useUrlImage && value != null && value.trim().isNotEmpty) {
                final uri = Uri.tryParse(value.trim());
                if (uri == null || !uri.hasScheme || (!uri.scheme.startsWith('http'))) {
                  return 'Please enter a valid URL';
                }
              }
              return null;
            },
            onChanged: (value) {
              setState(() {}); // Refresh to update image preview
            },
          ),
        ],
      ],
    );
  }

  Widget _buildImageContent() {
    // Show URL image preview if URL is being used
    if (_useUrlImage && _imageUrlController.text.trim().isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Image.network(
          _imageUrlController.text.trim(),
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, size: 50, color: Colors.red),
                  SizedBox(height: 8),
                  Text('Invalid image URL', style: TextStyle(color: Colors.red)),
                ],
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        ),
      );
    }
    // Show selected file image
    else if (_selectedImage != null) {
      if (kIsWeb && _webImage != null) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          child: Image.memory(
            _webImage!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
        );
      } else {
        return ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          child: Image.file(
            File(_selectedImage!.path),
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
        );
      }
    } else if (_existingImageUrl != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Image.network(
          _existingImageUrl!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return const Center(
              child: Icon(Icons.broken_image, size: 50),
            );
          },
        ),
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image,
              size: 50,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'No image selected',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }
  }
}
