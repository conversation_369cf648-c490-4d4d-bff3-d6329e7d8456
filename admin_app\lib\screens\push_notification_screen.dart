import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../services/push_notification_service.dart';
import '../widgets/common/loading_widget.dart';
import 'fcm_settings_screen.dart';

class PushNotificationScreen extends StatefulWidget {
  const PushNotificationScreen({super.key});

  @override
  State<PushNotificationScreen> createState() => _PushNotificationScreenState();
}

class _PushNotificationScreenState extends State<PushNotificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  
  bool _isLoading = false;
  String _selectedUserType = 'all';
  int _totalUsers = 0;
  int _totalResellers = 0;
  int _totalAdmins = 0;

  final List<Map<String, String>> _userTypes = [
    {'value': 'all', 'label': 'All Users'},
    {'value': 'users', 'label': 'Regular Users Only'},
    {'value': 'resellers', 'label': 'Resellers Only'},
    {'value': 'admins', 'label': 'Admins Only'},
  ];

  @override
  void initState() {
    super.initState();
    _loadUserCounts();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _loadUserCounts() async {
    try {
      final counts = await PushNotificationService.getUserCounts();
      if (mounted) {
        setState(() {
          _totalUsers = counts['users'] ?? 0;
          _totalResellers = counts['resellers'] ?? 0;
          _totalAdmins = counts['admins'] ?? 0;
        });
      }
    } catch (e) {
      print('Error loading user counts: $e');
    }
  }

  int _getTargetUserCount() {
    switch (_selectedUserType) {
      case 'users':
        return _totalUsers;
      case 'resellers':
        return _totalResellers;
      case 'admins':
        return _totalAdmins;
      case 'all':
      default:
        return _totalUsers + _totalResellers + _totalAdmins;
    }
  }

  Future<void> _sendNotification() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog();
    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await PushNotificationService.sendBulkNotification(
        title: _titleController.text.trim(),
        message: _messageController.text.trim(),
        userType: _selectedUserType,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notification sent successfully to all users!'),
              backgroundColor: AppConstants.successColor,
              duration: Duration(seconds: 3),
            ),
          );
          _clearForm();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send notification. Please try again.'),
              backgroundColor: AppConstants.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _showConfirmationDialog() async {
    final targetCount = _getTargetUserCount();
    final userTypeLabel = _userTypes.firstWhere(
      (type) => type['value'] == _selectedUserType,
      orElse: () => {'label': 'All Users'},
    )['label'];

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Send Notification'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to send this notification?'),
            const SizedBox(height: 16),
            Text('Target: $userTypeLabel'),
            Text('Recipients: $targetCount users'),
            const SizedBox(height: 16),
            Text(
              'Title: ${_titleController.text.trim()}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('Message: ${_messageController.text.trim()}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
            ),
            child: const Text('Send Notification'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _clearForm() {
    _titleController.clear();
    _messageController.clear();
    setState(() {
      _selectedUserType = 'all';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppConstants.backgroundColor,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: _buildForm(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isSmallScreen = constraints.maxWidth < 600;

          if (isSmallScreen) {
            // Stack layout for small screens to prevent overflow
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.notifications_active,
                      size: AppConstants.iconSizeLarge,
                      color: AppConstants.primaryColor,
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Text(
                        'Send Push Notification',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeHeading,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimaryColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const FCMSettingsScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.settings),
                      tooltip: 'FCM Settings',
                      color: AppConstants.primaryColor,
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                _buildUserCountsInfo(),
              ],
            );
          }

          // Original row layout for larger screens
          return Row(
            children: [
              const Icon(
                Icons.notifications_active,
                size: AppConstants.iconSizeLarge,
                color: AppConstants.primaryColor,
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              const Expanded(
                child: Text(
                  'Send Push Notification',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeHeading,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimaryColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const FCMSettingsScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.settings),
                tooltip: 'FCM Settings',
                color: AppConstants.primaryColor,
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              _buildUserCountsInfo(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildUserCountsInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'User Statistics',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
          const SizedBox(height: 4),
          Text('Total Users: $_totalUsers'),
          Text('Resellers: $_totalResellers'),
          Text('Admins: $_totalAdmins'),
        ],
      ),
    );
  }

  Widget _buildFCMConfigurationWarning() {
    return FutureBuilder<bool>(
      future: _checkFCMConfiguration(),
      builder: (context, snapshot) {
        if (snapshot.data == false) {
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.warningColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              border: Border.all(color: AppConstants.warningColor.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.warning,
                  color: AppConstants.warningColor,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                const Expanded(
                  child: Text(
                    'FCM Server Key not configured. Please configure it in settings to send notifications.',
                    style: TextStyle(color: AppConstants.warningColor),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const FCMSettingsScreen(),
                      ),
                    );
                  },
                  child: const Text('Configure'),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Future<bool> _checkFCMConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final serverKey = prefs.getString('fcm_server_key');
      return serverKey != null && serverKey.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFCMConfigurationWarning(),
          _buildUserTypeSelector(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildTitleField(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildMessageField(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildTargetInfo(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildSendButton(),
        ],
      ),
    );
  }

  Widget _buildUserTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Target Audience',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
          decoration: BoxDecoration(
            border: Border.all(color: AppConstants.borderColor),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedUserType,
              isExpanded: true,
              onChanged: (value) {
                setState(() {
                  _selectedUserType = value!;
                });
              },
              items: _userTypes.map((type) {
                return DropdownMenuItem<String>(
                  value: type['value'],
                  child: Text(type['label']!),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Notification Title',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: _titleController,
          decoration: InputDecoration(
            hintText: 'Enter notification title',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Title is required';
            }
            if (value.trim().length > 100) {
              return 'Title must be less than 100 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildMessageField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Notification Message',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: _messageController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'Enter notification message',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Message is required';
            }
            if (value.trim().length > 500) {
              return 'Message must be less than 500 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildTargetInfo() {
    final targetCount = _getTargetUserCount();
    final userTypeLabel = _userTypes.firstWhere(
      (type) => type['value'] == _selectedUserType,
      orElse: () => {'label': 'All Users'},
    )['label'];

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.primaryColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Target: $userTypeLabel',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'This notification will be sent to $targetCount users',
                  style: const TextStyle(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _sendNotification,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Send Notification',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
      ),
    );
  }
}
