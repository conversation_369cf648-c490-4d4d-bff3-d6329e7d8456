import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/notification_model.dart';
import '../constants/app_constants.dart';

class NotificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = AppConstants.notificationsCollection;

  // Create a new notification
  static Future<bool> createNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required NotificationType type,
    required String title,
    required String message,
    String? relatedId,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Don't create notification for self
      if (userId == fromUserId) return false;

      final notificationId = _firestore.collection(_collection).doc().id;
      
      final notification = NotificationModel(
        id: notificationId,
        userId: userId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        fromUserAvatar: fromUserAvatar,
        type: type,
        title: title,
        message: message,
        relatedId: relatedId,
        data: data,
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .set(notification.toMap());

      return true;
    } catch (e) {
      print('Error creating notification: $e');
      return false;
    }
  }

  // Get notifications for a user
  static Future<List<NotificationModel>> getUserNotifications(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      return querySnapshot.docs
          .map((doc) => NotificationModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching notifications: $e');
      return [];
    }
  }

  // Get unread notifications count
  static Future<int> getUnreadNotificationsCount(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      print('Error fetching unread count: $e');
      return 0;
    }
  }

  // Get unread notifications count stream
  static Stream<int> getUnreadNotificationsCountStream(String userId) {
    return _firestore
        .collection(_collection)
        .where('userId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Mark notification as read
  static Future<bool> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .update({
        'isRead': true,
        'readAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read for a user
  static Future<bool> markAllAsRead(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'readAt': Timestamp.fromDate(DateTime.now()),
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error marking all notifications as read: $e');
      return false;
    }
  }

  // Delete notification
  static Future<bool> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .delete();

      return true;
    } catch (e) {
      print('Error deleting notification: $e');
      return false;
    }
  }

  // Get real-time notifications stream
  static Stream<List<NotificationModel>> getNotificationsStream(String userId) {
    return _firestore
        .collection(_collection)
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => NotificationModel.fromMap(doc.data()))
            .toList());
  }

  // Get all admin notifications (for admin users)
  static Stream<List<NotificationModel>> getAdminNotificationsStream() {
    print('Starting admin notifications stream...');

    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .limit(100)
        .snapshots()
        .handleError((error) {
          print('Firestore stream error: $error');
        })
        .map((snapshot) {
          print('Received ${snapshot.docs.length} notifications from Firestore');

          final adminNotificationTypes = [
            'userRegistration',
            'resellerApplication',
            'postReported',
            'userVerification',
            'system'
          ];

          final notifications = snapshot.docs
              .map((doc) {
                try {
                  final data = doc.data();
                  print('Processing notification ${doc.id} with type: ${data['type']}');
                  return NotificationModel.fromMap(data);
                } catch (e) {
                  print('Error parsing notification ${doc.id}: $e');
                  return null;
                }
              })
              .where((notification) => notification != null)
              .cast<NotificationModel>()
              .where((notification) => adminNotificationTypes.contains(notification.type.toString().split('.').last))
              .toList();

          print('Filtered to ${notifications.length} admin notifications');
          return notifications;
        });
  }

  // Get admin notifications (non-streaming version)
  static Future<List<NotificationModel>> getAdminNotifications() async {
    try {
      final adminNotificationTypes = [
        'userRegistration',
        'resellerApplication',
        'postReported',
        'userVerification',
        'system'
      ];

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(100)
          .get();

      return querySnapshot.docs
          .map((doc) {
            try {
              return NotificationModel.fromMap(doc.data());
            } catch (e) {
              print('Error parsing notification ${doc.id}: $e');
              return null;
            }
          })
          .where((notification) => notification != null &&
                 adminNotificationTypes.contains(notification!.type.toString().split('.').last))
          .cast<NotificationModel>()
          .toList();
    } catch (e) {
      print('Error getting admin notifications: $e');
      return [];
    }
  }

  // Helper methods for admin-specific notifications

  // Create test admin notification
  static Future<bool> createTestAdminNotification() async {
    try {
      final notificationId = _firestore.collection(_collection).doc().id;

      final notification = NotificationModel(
        id: notificationId,
        userId: 'admin', // Generic admin user ID
        fromUserId: 'system',
        fromUserName: 'System',
        fromUserAvatar: '',
        type: NotificationType.system,
        title: 'Test Admin Notification',
        message: 'This is a test notification to verify the admin notification system is working.',
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .set(notification.toMap());

      print('Test admin notification created successfully');
      return true;
    } catch (e) {
      print('Error creating test admin notification: $e');
      return false;
    }
  }

  // Create user registration notification for admins
  static Future<bool> createUserRegistrationNotification({
    required String newUserId,
    required String newUserName,
    required String newUserAvatar,
    required List<String> adminUserIds,
  }) async {
    bool allSuccess = true;
    
    for (String adminId in adminUserIds) {
      final success = await createNotification(
        userId: adminId,
        fromUserId: newUserId,
        fromUserName: newUserName,
        fromUserAvatar: newUserAvatar,
        type: NotificationType.userRegistration,
        title: 'New User Registration',
        message: '$newUserName has registered as a new user',
        relatedId: newUserId,
        data: {
          'newUserId': newUserId,
        },
      );
      if (!success) allSuccess = false;
    }
    
    return allSuccess;
  }

  // Create reseller application notification for admins
  static Future<bool> createResellerApplicationNotification({
    required String applicantId,
    required String applicantName,
    required String applicantAvatar,
    required List<String> adminUserIds,
  }) async {
    bool allSuccess = true;
    
    for (String adminId in adminUserIds) {
      final success = await createNotification(
        userId: adminId,
        fromUserId: applicantId,
        fromUserName: applicantName,
        fromUserAvatar: applicantAvatar,
        type: NotificationType.resellerApplication,
        title: 'New Reseller Application',
        message: '$applicantName has applied to become a reseller',
        relatedId: applicantId,
        data: {
          'applicantId': applicantId,
        },
      );
      if (!success) allSuccess = false;
    }
    
    return allSuccess;
  }

  // Create post reported notification for admins
  static Future<bool> createPostReportedNotification({
    required String postId,
    required String reporterId,
    required String reporterName,
    required String reporterAvatar,
    required String reason,
    required List<String> adminUserIds,
  }) async {
    bool allSuccess = true;
    
    for (String adminId in adminUserIds) {
      final success = await createNotification(
        userId: adminId,
        fromUserId: reporterId,
        fromUserName: reporterName,
        fromUserAvatar: reporterAvatar,
        type: NotificationType.postReported,
        title: 'Post Reported',
        message: '$reporterName reported a post for: $reason',
        relatedId: postId,
        data: {
          'postId': postId,
          'reporterId': reporterId,
          'reason': reason,
        },
      );
      if (!success) allSuccess = false;
    }
    
    return allSuccess;
  }
}
