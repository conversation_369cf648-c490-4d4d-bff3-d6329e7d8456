import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';

class FCMv1Service {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // FCM v1 API endpoint
  static const String _projectId = 'play-integrity-snipydrrvihjxfz';
  static String get _fcmV1Url => 'https://fcm.googleapis.com/v1/projects/$_projectId/messages:send';
  
  // OAuth2 endpoint for getting access token
  static const String _oauthUrl = 'https://oauth2.googleapis.com/token';
  
  /// Get OAuth2 access token using service account key
  static Future<String?> _getAccessToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final serviceAccountJson = prefs.getString('fcm_service_account_key');
      
      if (serviceAccountJson == null || serviceAccountJson.isEmpty) {
        print('FCM Service Account Key not configured. Please set it in FCM Settings.');
        return null;
      }
      
      final serviceAccount = json.decode(serviceAccountJson);
      
      // Create JWT for OAuth2
      final jwt = await _createJWT(serviceAccount);
      if (jwt == null) return null;
      
      // Exchange JWT for access token
      final response = await http.post(
        Uri.parse(_oauthUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
          'assertion': jwt,
        },
      );
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['access_token'];
      } else {
        print('OAuth2 Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error getting access token: $e');
      return null;
    }
  }
  
  /// Create JWT for OAuth2 authentication
  static Future<String?> _createJWT(Map<String, dynamic> serviceAccount) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      final payload = {
        'iss': serviceAccount['client_email'],
        'scope': 'https://www.googleapis.com/auth/firebase.messaging',
        'aud': 'https://oauth2.googleapis.com/token',
        'exp': now + 3600, // 1 hour
        'iat': now,
      };

      // Create JWT using the dart_jsonwebtoken package
      final jwt = JWT(payload);

      // Sign with RSA private key
      final privateKey = serviceAccount['private_key'] as String;
      final token = jwt.sign(RSAPrivateKey(privateKey), algorithm: JWTAlgorithm.RS256);

      return token;

    } catch (e) {
      print('Error creating JWT: $e');
      return null;
    }
  }
  
  /// Send notification using FCM v1 API
  static Future<bool> sendNotificationV1({
    required String token,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        print('Failed to get access token');
        return false;
      }
      
      final message = {
        'message': {
          'token': token,
          'notification': {
            'title': title,
            'body': body,
          },
          'data': data ?? {},
          'android': {
            'priority': 'high',
            'notification': {
              'sound': 'default',
              'channel_id': 'default',
            },
          },
          'apns': {
            'payload': {
              'aps': {
                'sound': 'default',
                'badge': 1,
              },
            },
          },
        },
      };
      
      final response = await http.post(
        Uri.parse(_fcmV1Url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode(message),
      );
      
      if (response.statusCode == 200) {
        print('FCM v1 notification sent successfully');
        return true;
      } else {
        print('FCM v1 Error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error sending FCM v1 notification: $e');
      return false;
    }
  }
  
  /// Send notification to multiple tokens using FCM v1 API
  static Future<bool> sendBulkNotificationV1({
    required List<String> tokens,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    if (tokens.isEmpty) return false;
    
    bool allSuccessful = true;
    
    // FCM v1 doesn't support bulk sending like legacy API
    // We need to send individual requests
    for (final token in tokens) {
      final success = await sendNotificationV1(
        token: token,
        title: title,
        body: body,
        data: data,
      );
      if (!success) {
        allSuccessful = false;
      }
      
      // Add small delay to avoid rate limiting
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    return allSuccessful;
  }
  
  /// Get FCM tokens for specific user type
  static Future<List<String>> getFCMTokens(String userType) async {
    try {
      Query query = _firestore.collection('users');

      switch (userType) {
        case 'users':
          query = query.where('role', isEqualTo: 'user');
          break;
        case 'resellers':
          query = query.where('role', isEqualTo: 'reseller');
          break;
        case 'admins':
          query = query.where('role', isEqualTo: 'admin');
          break;
        case 'all':
        default:
          // No additional filter for all users
          break;
      }

      final snapshot = await query.get();
      final tokens = <String>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final fcmToken = data['fcmToken'] as String?;
        if (fcmToken != null && fcmToken.isNotEmpty) {
          tokens.add(fcmToken);
        }
      }

      return tokens;
    } catch (e) {
      print('Error getting FCM tokens: $e');
      return [];
    }
  }
  
  /// Send bulk notification to users by type using FCM v1
  static Future<bool> sendBulkNotificationToUserType({
    required String title,
    required String body,
    required String userType,
    Map<String, String>? data,
  }) async {
    try {
      final tokens = await getFCMTokens(userType);
      
      if (tokens.isEmpty) {
        print('No FCM tokens found for user type: $userType');
        return false;
      }

      print('Sending FCM v1 notification to ${tokens.length} users');

      final success = await sendBulkNotificationV1(
        tokens: tokens,
        title: title,
        body: body,
        data: data,
      );

      // Save notification record
      await _saveNotificationRecord(
        title: title,
        message: body,
        userType: userType,
        recipientCount: tokens.length,
      );

      return success;
    } catch (e) {
      print('Error sending bulk notification: $e');
      return false;
    }
  }
  
  /// Save notification record to database
  static Future<void> _saveNotificationRecord({
    required String title,
    required String message,
    required String userType,
    required int recipientCount,
  }) async {
    try {
      await _firestore.collection('admin_notifications').add({
        'title': title,
        'message': message,
        'userType': userType,
        'recipientCount': recipientCount,
        'sentAt': FieldValue.serverTimestamp(),
        'sentBy': 'admin',
        'apiVersion': 'v1',
      });
    } catch (e) {
      print('Error saving notification record: $e');
    }
  }
}
