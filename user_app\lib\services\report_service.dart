import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/report_model.dart';
import '../constants/app_constants.dart';

class ReportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'reports';

  /// Submit a new report
  static Future<bool> submitReport({
    required String reporterId,
    required String reporterName,
    required String reporterEmail,
    String? reporterAvatar,
    required ReportType type,
    required String targetId,
    String? targetUserId,
    String? targetUserName,
    required ReportReason reason,
    String? description,
  }) async {
    try {
      final reportId = _firestore.collection(_collection).doc().id;
      
      final report = ReportModel(
        id: reportId,
        reporterId: reporterId,
        reporterName: reporterName,
        reporterEmail: reporterEmail,
        reporterAvatar: reporterAvatar,
        type: type,
        targetId: targetId,
        targetUserId: targetUserId,
        targetUserName: targetUserName,
        reason: reason,
        description: description,
        status: ReportStatus.pending,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore.collection(_collection).doc(reportId).set(report.toMap());

      // Notify admins about the new report
      await _notifyAdminsAboutReport(report);

      return true;
    } catch (e) {
      print('Error submitting report: $e');
      return false;
    }
  }

  /// Get reports by reporter ID
  static Future<List<ReportModel>> getReportsByReporter(String reporterId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('reporterId', isEqualTo: reporterId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting reports by reporter: $e');
      return [];
    }
  }

  /// Get all reports (admin only)
  static Future<List<ReportModel>> getAllReports({
    int limit = 50,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting all reports: $e');
      return [];
    }
  }

  /// Get reports by status (admin only)
  static Future<List<ReportModel>> getReportsByStatus(ReportStatus status) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: status.value)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting reports by status: $e');
      return [];
    }
  }

  /// Get reports by type (admin only)
  static Future<List<ReportModel>> getReportsByType(ReportType type) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('type', isEqualTo: type.value)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting reports by type: $e');
      return [];
    }
  }

  /// Update report status (admin only)
  static Future<bool> updateReportStatus({
    required String reportId,
    required ReportStatus status,
    String? adminNote,
    String? adminId,
    String? adminName,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.value,
        'updatedAt': Timestamp.now(),
      };

      if (adminNote != null) {
        updateData['adminNote'] = adminNote;
      }

      if (adminId != null) {
        updateData['adminId'] = adminId;
      }

      if (adminName != null) {
        updateData['adminName'] = adminName;
      }

      if (status == ReportStatus.reviewed) {
        updateData['reviewedAt'] = Timestamp.now();
      } else if (status == ReportStatus.resolved) {
        updateData['resolvedAt'] = Timestamp.now();
      }

      await _firestore.collection(_collection).doc(reportId).update(updateData);
      return true;
    } catch (e) {
      print('Error updating report status: $e');
      return false;
    }
  }

  /// Get report statistics (admin only)
  static Future<Map<String, int>> getReportStatistics() async {
    try {
      final allReportsSnapshot = await _firestore.collection(_collection).get();
      final pendingReportsSnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReportStatus.pending.value)
          .get();
      final reviewedReportsSnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReportStatus.reviewed.value)
          .get();
      final resolvedReportsSnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReportStatus.resolved.value)
          .get();

      return {
        'total': allReportsSnapshot.docs.length,
        'pending': pendingReportsSnapshot.docs.length,
        'reviewed': reviewedReportsSnapshot.docs.length,
        'resolved': resolvedReportsSnapshot.docs.length,
        'dismissed': allReportsSnapshot.docs.length - 
                    pendingReportsSnapshot.docs.length - 
                    reviewedReportsSnapshot.docs.length - 
                    resolvedReportsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error getting report statistics: $e');
      return {
        'total': 0,
        'pending': 0,
        'reviewed': 0,
        'resolved': 0,
        'dismissed': 0,
      };
    }
  }

  /// Check if user has already reported a target
  static Future<bool> hasUserReported({
    required String reporterId,
    required String targetId,
    required ReportType type,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('reporterId', isEqualTo: reporterId)
          .where('targetId', isEqualTo: targetId)
          .where('type', isEqualTo: type.value)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking if user has reported: $e');
      return false;
    }
  }

  /// Delete report (admin only)
  static Future<bool> deleteReport(String reportId) async {
    try {
      await _firestore.collection(_collection).doc(reportId).delete();
      return true;
    } catch (e) {
      print('Error deleting report: $e');
      return false;
    }
  }

  /// Private method to notify admins about new reports
  static Future<void> _notifyAdminsAboutReport(ReportModel report) async {
    try {
      // Get all admin users
      final adminSnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: 'admin')
          .where('isActive', isEqualTo: true)
          .get();

      // Create notification for each admin
      for (final adminDoc in adminSnapshot.docs) {
        await _firestore.collection('notifications').add({
          'userId': adminDoc.id,
          'fromUserId': report.reporterId,
          'fromUserName': report.reporterName,
          'fromUserAvatar': report.reporterAvatar,
          'type': 'report_submitted',
          'title': 'New Report Submitted',
          'message': '${report.reporterName} reported a ${report.type.value} for ${report.reason.displayName}',
          'relatedId': report.id,
          'data': {
            'reportId': report.id,
            'reportType': report.type.value,
            'targetId': report.targetId,
            'reason': report.reason.value,
          },
          'isRead': false,
          'createdAt': Timestamp.now(),
        });
      }
    } catch (e) {
      print('Error notifying admins about report: $e');
    }
  }
}
