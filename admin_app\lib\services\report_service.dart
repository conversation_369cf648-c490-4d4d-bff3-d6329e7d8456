import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/report_model.dart';
import '../constants/app_constants.dart';

class ReportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'reports';

  /// Get all reports (admin only)
  static Future<List<ReportModel>> getAllReports({
    int limit = 50,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting all reports: $e');
      return [];
    }
  }

  /// Get reports by status (admin only)
  static Future<List<ReportModel>> getReportsByStatus(ReportStatus status) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: status.value)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting reports by status: $e');
      return [];
    }
  }

  /// Get reports by type (admin only)
  static Future<List<ReportModel>> getReportsByType(ReportType type) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('type', isEqualTo: type.value)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting reports by type: $e');
      return [];
    }
  }

  /// Update report status (admin only)
  static Future<bool> updateReportStatus({
    required String reportId,
    required ReportStatus status,
    String? adminNote,
    String? adminId,
    String? adminName,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.value,
        'updatedAt': Timestamp.now(),
      };

      if (adminNote != null) {
        updateData['adminNote'] = adminNote;
      }

      if (adminId != null) {
        updateData['adminId'] = adminId;
      }

      if (adminName != null) {
        updateData['adminName'] = adminName;
      }

      if (status == ReportStatus.reviewed) {
        updateData['reviewedAt'] = Timestamp.now();
      } else if (status == ReportStatus.resolved) {
        updateData['resolvedAt'] = Timestamp.now();
      }

      await _firestore.collection(_collection).doc(reportId).update(updateData);
      return true;
    } catch (e) {
      print('Error updating report status: $e');
      return false;
    }
  }

  /// Get report statistics (admin only)
  static Future<Map<String, int>> getReportStatistics() async {
    try {
      final allReportsSnapshot = await _firestore.collection(_collection).get();
      final pendingReportsSnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReportStatus.pending.value)
          .get();
      final reviewedReportsSnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReportStatus.reviewed.value)
          .get();
      final resolvedReportsSnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: ReportStatus.resolved.value)
          .get();

      return {
        'total': allReportsSnapshot.docs.length,
        'pending': pendingReportsSnapshot.docs.length,
        'reviewed': reviewedReportsSnapshot.docs.length,
        'resolved': resolvedReportsSnapshot.docs.length,
        'dismissed': allReportsSnapshot.docs.length - 
                    pendingReportsSnapshot.docs.length - 
                    reviewedReportsSnapshot.docs.length - 
                    resolvedReportsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error getting report statistics: $e');
      return {
        'total': 0,
        'pending': 0,
        'reviewed': 0,
        'resolved': 0,
        'dismissed': 0,
      };
    }
  }

  /// Get report by ID (admin only)
  static Future<ReportModel?> getReportById(String reportId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(reportId).get();
      if (doc.exists) {
        return ReportModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting report by ID: $e');
      return null;
    }
  }

  /// Delete report (admin only)
  static Future<bool> deleteReport(String reportId) async {
    try {
      await _firestore.collection(_collection).doc(reportId).delete();
      return true;
    } catch (e) {
      print('Error deleting report: $e');
      return false;
    }
  }

  /// Get reports with pagination
  static Future<List<ReportModel>> getReportsPaginated({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    ReportStatus? status,
    ReportType? type,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }

      if (type != null) {
        query = query.where('type', isEqualTo: type.value);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ReportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting reports paginated: $e');
      return [];
    }
  }

  /// Bulk update report status (admin only)
  static Future<bool> bulkUpdateReportStatus({
    required List<String> reportIds,
    required ReportStatus status,
    String? adminNote,
    String? adminId,
    String? adminName,
  }) async {
    try {
      final batch = _firestore.batch();

      for (final reportId in reportIds) {
        final updateData = <String, dynamic>{
          'status': status.value,
          'updatedAt': Timestamp.now(),
        };

        if (adminNote != null) {
          updateData['adminNote'] = adminNote;
        }

        if (adminId != null) {
          updateData['adminId'] = adminId;
        }

        if (adminName != null) {
          updateData['adminName'] = adminName;
        }

        if (status == ReportStatus.reviewed) {
          updateData['reviewedAt'] = Timestamp.now();
        } else if (status == ReportStatus.resolved) {
          updateData['resolvedAt'] = Timestamp.now();
        }

        batch.update(
          _firestore.collection(_collection).doc(reportId),
          updateData,
        );
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error bulk updating report status: $e');
      return false;
    }
  }

  /// Get reports count by status
  static Future<Map<String, int>> getReportsCountByStatus() async {
    try {
      final futures = await Future.wait([
        _firestore.collection(_collection).where('status', isEqualTo: ReportStatus.pending.value).get(),
        _firestore.collection(_collection).where('status', isEqualTo: ReportStatus.reviewed.value).get(),
        _firestore.collection(_collection).where('status', isEqualTo: ReportStatus.resolved.value).get(),
        _firestore.collection(_collection).where('status', isEqualTo: ReportStatus.dismissed.value).get(),
      ]);

      return {
        'pending': futures[0].docs.length,
        'reviewed': futures[1].docs.length,
        'resolved': futures[2].docs.length,
        'dismissed': futures[3].docs.length,
      };
    } catch (e) {
      print('Error getting reports count by status: $e');
      return {
        'pending': 0,
        'reviewed': 0,
        'resolved': 0,
        'dismissed': 0,
      };
    }
  }
}
