import 'package:cloud_firestore/cloud_firestore.dart';

enum ReportType {
  user,
  post,
  comment,
  product,
}

enum ReportStatus {
  pending,
  reviewed,
  resolved,
  dismissed,
}

enum ReportReason {
  spam,
  harassment,
  inappropriateContent,
  fakeAccount,
  scam,
  violence,
  hateSpeech,
  other,
}

extension ReportTypeExtension on ReportType {
  String get value {
    switch (this) {
      case ReportType.user:
        return 'user';
      case ReportType.post:
        return 'post';
      case ReportType.comment:
        return 'comment';
      case ReportType.product:
        return 'product';
    }
  }

  static ReportType fromString(String value) {
    switch (value) {
      case 'user':
        return ReportType.user;
      case 'post':
        return ReportType.post;
      case 'comment':
        return ReportType.comment;
      case 'product':
        return ReportType.product;
      default:
        return ReportType.user;
    }
  }
}

extension ReportStatusExtension on ReportStatus {
  String get value {
    switch (this) {
      case ReportStatus.pending:
        return 'pending';
      case ReportStatus.reviewed:
        return 'reviewed';
      case ReportStatus.resolved:
        return 'resolved';
      case ReportStatus.dismissed:
        return 'dismissed';
    }
  }

  static ReportStatus fromString(String value) {
    switch (value) {
      case 'pending':
        return ReportStatus.pending;
      case 'reviewed':
        return ReportStatus.reviewed;
      case 'resolved':
        return ReportStatus.resolved;
      case 'dismissed':
        return ReportStatus.dismissed;
      default:
        return ReportStatus.pending;
    }
  }
}

extension ReportReasonExtension on ReportReason {
  String get value {
    switch (this) {
      case ReportReason.spam:
        return 'spam';
      case ReportReason.harassment:
        return 'harassment';
      case ReportReason.inappropriateContent:
        return 'inappropriate_content';
      case ReportReason.fakeAccount:
        return 'fake_account';
      case ReportReason.scam:
        return 'scam';
      case ReportReason.violence:
        return 'violence';
      case ReportReason.hateSpeech:
        return 'hate_speech';
      case ReportReason.other:
        return 'other';
    }
  }

  String get displayName {
    switch (this) {
      case ReportReason.spam:
        return 'Spam';
      case ReportReason.harassment:
        return 'Harassment';
      case ReportReason.inappropriateContent:
        return 'Inappropriate Content';
      case ReportReason.fakeAccount:
        return 'Fake Account';
      case ReportReason.scam:
        return 'Scam';
      case ReportReason.violence:
        return 'Violence';
      case ReportReason.hateSpeech:
        return 'Hate Speech';
      case ReportReason.other:
        return 'Other';
    }
  }

  static ReportReason fromString(String value) {
    switch (value) {
      case 'spam':
        return ReportReason.spam;
      case 'harassment':
        return ReportReason.harassment;
      case 'inappropriate_content':
        return ReportReason.inappropriateContent;
      case 'fake_account':
        return ReportReason.fakeAccount;
      case 'scam':
        return ReportReason.scam;
      case 'violence':
        return ReportReason.violence;
      case 'hate_speech':
        return ReportReason.hateSpeech;
      case 'other':
        return ReportReason.other;
      default:
        return ReportReason.other;
    }
  }
}

class ReportModel {
  final String id;
  final String reporterId;
  final String reporterName;
  final String reporterEmail;
  final String? reporterAvatar;
  final ReportType type;
  final String targetId; // ID of the reported user/post/comment/product
  final String? targetUserId; // User ID of the target (for posts/comments/products)
  final String? targetUserName;
  final ReportReason reason;
  final String? description;
  final ReportStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? adminNote;
  final String? adminId;
  final String? adminName;
  final DateTime? reviewedAt;
  final DateTime? resolvedAt;

  ReportModel({
    required this.id,
    required this.reporterId,
    required this.reporterName,
    required this.reporterEmail,
    this.reporterAvatar,
    required this.type,
    required this.targetId,
    this.targetUserId,
    this.targetUserName,
    required this.reason,
    this.description,
    this.status = ReportStatus.pending,
    required this.createdAt,
    required this.updatedAt,
    this.adminNote,
    this.adminId,
    this.adminName,
    this.reviewedAt,
    this.resolvedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reporterId': reporterId,
      'reporterName': reporterName,
      'reporterEmail': reporterEmail,
      'reporterAvatar': reporterAvatar,
      'type': type.value,
      'targetId': targetId,
      'targetUserId': targetUserId,
      'targetUserName': targetUserName,
      'reason': reason.value,
      'description': description,
      'status': status.value,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'adminNote': adminNote,
      'adminId': adminId,
      'adminName': adminName,
      'reviewedAt': reviewedAt != null ? Timestamp.fromDate(reviewedAt!) : null,
      'resolvedAt': resolvedAt != null ? Timestamp.fromDate(resolvedAt!) : null,
    };
  }

  factory ReportModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReportModel(
      id: doc.id,
      reporterId: data['reporterId'] ?? '',
      reporterName: data['reporterName'] ?? '',
      reporterEmail: data['reporterEmail'] ?? '',
      reporterAvatar: data['reporterAvatar'],
      type: ReportTypeExtension.fromString(data['type'] ?? 'user'),
      targetId: data['targetId'] ?? '',
      targetUserId: data['targetUserId'],
      targetUserName: data['targetUserName'],
      reason: ReportReasonExtension.fromString(data['reason'] ?? 'other'),
      description: data['description'],
      status: ReportStatusExtension.fromString(data['status'] ?? 'pending'),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      adminNote: data['adminNote'],
      adminId: data['adminId'],
      adminName: data['adminName'],
      reviewedAt: (data['reviewedAt'] as Timestamp?)?.toDate(),
      resolvedAt: (data['resolvedAt'] as Timestamp?)?.toDate(),
    );
  }

  ReportModel copyWith({
    String? id,
    String? reporterId,
    String? reporterName,
    String? reporterEmail,
    String? reporterAvatar,
    ReportType? type,
    String? targetId,
    String? targetUserId,
    String? targetUserName,
    ReportReason? reason,
    String? description,
    ReportStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? adminNote,
    String? adminId,
    String? adminName,
    DateTime? reviewedAt,
    DateTime? resolvedAt,
  }) {
    return ReportModel(
      id: id ?? this.id,
      reporterId: reporterId ?? this.reporterId,
      reporterName: reporterName ?? this.reporterName,
      reporterEmail: reporterEmail ?? this.reporterEmail,
      reporterAvatar: reporterAvatar ?? this.reporterAvatar,
      type: type ?? this.type,
      targetId: targetId ?? this.targetId,
      targetUserId: targetUserId ?? this.targetUserId,
      targetUserName: targetUserName ?? this.targetUserName,
      reason: reason ?? this.reason,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      adminNote: adminNote ?? this.adminNote,
      adminId: adminId ?? this.adminId,
      adminName: adminName ?? this.adminName,
      reviewedAt: reviewedAt ?? this.reviewedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
    );
  }
}
