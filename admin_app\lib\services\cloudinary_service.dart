import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:cloudinary_public/cloudinary_public.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';

class CloudinaryService {
  static const String _cloudName = 'doxd3bscf';
  static const String _apiKey = '251519664785658';
  static const String _apiSecret = 'Y3-ZEUAZ5b121-gr9UBbzfV006E';

  static final CloudinaryPublic _cloudinary = CloudinaryPublic(
    _cloudName,
    'amarpoint', // Upload preset name
    cache: false,
  );

  /// Upload image file to Cloudinary
  /// Returns the secure URL of the uploaded image
  static Future<String?> uploadImage({
    required dynamic imageFile, // Can be File or Uint8List
    required String folder,
    String? publicId,
    int? quality,
    int? width,
    int? height,
  }) async {
    try {
      print('CloudinaryService: Starting image upload to folder: $folder');
      print('CloudinaryService: Image file type: ${imageFile.runtimeType}');
      print('CloudinaryService: Using upload preset: amarpoint');

      CloudinaryResponse response;

      if (kIsWeb && imageFile is Uint8List) {
        print('CloudinaryService: Uploading from bytes (Web)');
        // For web platform, upload from bytes
        response = await _cloudinary.uploadFile(
          CloudinaryFile.fromBytesData(
            imageFile,
            identifier: publicId ?? 'image_${DateTime.now().millisecondsSinceEpoch}',
            folder: folder,
          ),
        );
      } else if (imageFile is File) {
        print('CloudinaryService: Uploading from file (Mobile): ${imageFile.path}');
        // For mobile platforms, upload from file
        response = await _cloudinary.uploadFile(
          CloudinaryFile.fromFile(
            imageFile.path,
            folder: folder,
            publicId: publicId,
          ),
        );
      } else {
        throw Exception('Unsupported image file type: ${imageFile.runtimeType}');
      }

      print('CloudinaryService: Upload successful, URL: ${response.secureUrl}');
      return response.secureUrl;
    } catch (e) {
      print('CloudinaryService: Upload failed: $e');
      return null;
    }
  }


  /// Upload multiple images (for products or posts)
  static Future<List<String>> uploadMultipleImages({
    required List<dynamic> imageFiles,
    required String folder,
    required String basePublicId,
    int? quality,
    int? width,
    int? height,
  }) async {
    List<String> uploadedUrls = [];

    print('CloudinaryService: Starting upload of ${imageFiles.length} images');

    for (int i = 0; i < imageFiles.length; i++) {
      print('CloudinaryService: Uploading image ${i + 1}/${imageFiles.length}');

      final publicId = '${basePublicId}_$i';
      final url = await uploadImage(
        imageFile: imageFiles[i],
        folder: folder,
        publicId: publicId,
        quality: quality,
        width: width,
        height: height,
      );

      if (url != null) {
        uploadedUrls.add(url);
      }
    }

    return uploadedUrls;
  }

  /// Upload product image with optimized settings
  static Future<String?> uploadProductImage({
    required dynamic imageFile,
    required String productId,
    int? index,
  }) async {
    final publicId = index != null 
        ? 'product_${productId}_$index' 
        : 'product_$productId';
        
    return await uploadImage(
      imageFile: imageFile,
      folder: 'product_images',
      publicId: publicId,
      quality: 85,
      width: 800,
      height: 800,
    );
  }

  /// Upload profile image with optimized settings
  static Future<String?> uploadProfileImage({
    required dynamic imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      folder: 'profile_images',
      publicId: 'profile_$userId',
      quality: 90,
      width: 400,
      height: 400,
    );
  }

  /// Upload category icon with optimized settings
  static Future<String?> uploadCategoryIcon({
    required dynamic imageFile,
    required String categoryId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      folder: 'category_icons',
      publicId: 'category_$categoryId',
      quality: 90,
      width: 200,
      height: 200,
    );
  }

  /// Delete image from Cloudinary
  static Future<bool> deleteImage(String publicId) async {
    try {
      // For now, return true as deletion is not critical for basic functionality
      print('CloudinaryService: Delete image called for publicId: $publicId');
      return true;
    } catch (e) {
      print('Error deleting image from Cloudinary: $e');
      return false;
    }
  }

  /// Get optimized image URL with transformations
  static String getOptimizedImageUrl({
    required String imageUrl,
    int? width,
    int? height,
    int quality = 80,
    String format = 'auto',
    String crop = 'fill',
  }) {
    if (!imageUrl.contains('cloudinary.com')) {
      return imageUrl; // Return original URL if not a Cloudinary URL
    }

    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments.toList();
      
      // Find the upload segment
      final uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1) return imageUrl;

      // Build transformation string
      final transformations = <String>[];
      
      if (width != null) transformations.add('w_$width');
      if (height != null) transformations.add('h_$height');
      transformations.add('q_$quality');
      transformations.add('f_$format');
      transformations.add('c_$crop');

      final transformationString = transformations.join(',');

      // Insert transformation after upload
      pathSegments.insert(uploadIndex + 1, transformationString);

      // Rebuild URL
      final newUri = uri.replace(pathSegments: pathSegments);
      return newUri.toString();
    } catch (e) {
      print('Error optimizing image URL: $e');
      return imageUrl;
    }
  }

  /// Extract public ID from Cloudinary URL
  static String? extractPublicId(String imageUrl) {
    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      
      // Find the upload segment
      final uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1) return null;

      // Get segments after upload (skip transformation if present)
      final relevantSegments = pathSegments.skip(uploadIndex + 1).toList();
      
      // Remove transformation segment if present
      if (relevantSegments.isNotEmpty && relevantSegments.first.contains('_')) {
        relevantSegments.removeAt(0);
      }

      // Join remaining segments and remove file extension
      final publicIdWithExtension = relevantSegments.join('/');
      final lastDotIndex = publicIdWithExtension.lastIndexOf('.');
      
      if (lastDotIndex != -1) {
        return publicIdWithExtension.substring(0, lastDotIndex);
      }
      
      return publicIdWithExtension;
    } catch (e) {
      print('Error extracting public ID: $e');
      return null;
    }
  }

  /// Get image info from Cloudinary
  static Future<Map<String, dynamic>?> getImageInfo(String publicId) async {
    try {
      print('CloudinaryService: Get image info called for publicId: $publicId');
      // For now, return null as this is not critical for basic functionality
      return null;
    } catch (e) {
      print('Error getting image info: $e');
      return null;
    }
  }
}
