import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/category_model.dart';
import '../services/category_service.dart';
import '../services/image_service.dart';

class ProductCategoryManagementScreen extends StatefulWidget {
  const ProductCategoryManagementScreen({super.key});

  @override
  State<ProductCategoryManagementScreen> createState() => _ProductCategoryManagementScreenState();
}

class _ProductCategoryManagementScreenState extends State<ProductCategoryManagementScreen> {
  List<CategoryModel> _categories = [];
  Map<String, int> _statistics = {};
  bool _isLoading = false;
  String _currentFilter = 'all';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadStatistics();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final categories = await CategoryService.getAllCategoriesPaginated(
        isActiveFilter: _currentFilter == 'all' ? null : _currentFilter == 'active',
        searchQuery: _searchController.text.trim().isEmpty ? null : _searchController.text.trim(),
      );

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load categories: $e')),
        );
      }
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await CategoryService.getCategoriesStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  String _getIconDisplayName(String iconName) {
    switch (iconName) {
      case 'category': return 'Category';
      case 'shopping_bag': return 'Shopping Bag';
      case 'devices': return 'Devices';
      case 'home': return 'Home';
      case 'sports': return 'Sports';
      case 'book': return 'Books';
      case 'car': return 'Automotive';
      case 'restaurant': return 'Food & Dining';
      case 'phone_android': return 'Phone & Android';
      case 'computer': return 'Computer';
      case 'watch': return 'Watch';
      case 'headphones': return 'Headphones';
      case 'camera': return 'Camera';
      case 'tv': return 'TV';
      case 'tablet': return 'Tablet';
      case 'kitchen': return 'Kitchen';
      case 'bed': return 'Bed';
      case 'chair': return 'Chair';
      case 'lamp': return 'Lamp';
      case 'garden': return 'Garden';
      case 'tools': return 'Tools';
      case 'paint': return 'Paint';
      case 'cleaning': return 'Cleaning';
      case 'fitness': return 'Fitness';
      case 'outdoor': return 'Outdoor';
      case 'games': return 'Games';
      case 'music': return 'Music';
      case 'art': return 'Art';
      case 'baby': return 'Baby';
      case 'pet': return 'Pet';
      case 'health': return 'Health';
      case 'beauty': return 'Beauty';
      case 'jewelry': return 'Jewelry';
      case 'clothing': return 'Clothing';
      case 'shoes': return 'Shoes';
      case 'bags': return 'Bags';
      case 'accessories': return 'Accessories';
      default: return iconName.replaceAll('_', ' ').split(' ').map((word) =>
        word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '').join(' ');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Category Management'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showAddCategoryDialog(),
            icon: const Icon(Icons.add),
            tooltip: 'Add Category',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatistics(),
          _buildFilters(),
          Expanded(
            child: _buildCategoriesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          
        ],
      ),
    );
  }
  Widget _buildStatistics() {
    if (_statistics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatCard('Total', _statistics['total'] ?? 0, AppConstants.primaryColor),
          _buildStatCard('Active', _statistics['active'] ?? 0, AppConstants.successColor),
          _buildStatCard('Parent', _statistics['parent'] ?? 0, AppConstants.infoColor),
          _buildStatCard('Subcategories', _statistics['subcategories'] ?? 0, AppConstants.warningColor),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, int value, Color color) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: AppConstants.fontSizeXLarge,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Row(
        children: [
          const Text(
            'Filter:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeMedium,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('All', 'all'),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Active', 'active'),
                  const SizedBox(width: AppConstants.paddingSmall),
                  _buildFilterChip('Inactive', 'inactive'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _currentFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _currentFilter = value;
        });
        _loadCategories();
      },
      selectedColor: AppConstants.primaryColor.withOpacity(0.2),
      checkmarkColor: AppConstants.primaryColor,
    );
  }

  Widget _buildCategoriesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_categories.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 64,
              color: AppConstants.textHintColor,
            ),
            SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No categories found',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadCategories();
        _loadStatistics();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: _buildCategoryItem(category),
          );
        },
      ),
    );
  }
  Widget _buildCategoryItem(CategoryModel category) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: category.isActive
                        ? AppConstants.primaryColor.withOpacity(0.1)
                        : AppConstants.textSecondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                  child: Icon(
                    category.hasIcon
                        ? _getIconFromName(category.iconName!)
                        : Icons.category,
                    color: category.isActive
                        ? AppConstants.primaryColor
                        : AppConstants.textSecondaryColor,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              category.name,
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeLarge,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          _buildCategoryStatusChip(category),
                        ],
                      ),
                      if (category.description.isNotEmpty)
                        Text(
                          category.description,
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Row(
                        children: [
                          if (category.isParentCategory)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppConstants.paddingSmall,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppConstants.infoColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              ),
                              child: const Text(
                                'Parent Category',
                                style: TextStyle(
                                  fontSize: AppConstants.fontSizeSmall,
                                  color: AppConstants.infoColor,
                                ),
                              ),
                            ),
                          if (category.hasSubcategories) ...[
                            const SizedBox(width: AppConstants.paddingSmall),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppConstants.paddingSmall,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppConstants.warningColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              ),
                              child: Text(
                                '${category.subcategoryIds.length} Subcategories',
                                style: const TextStyle(
                                  fontSize: AppConstants.fontSizeSmall,
                                  color: AppConstants.warningColor,
                                ),
                              ),
                            ),
                          ],
                          const Spacer(),
                          Text(
                            'Created: ${category.formattedCreatedAt}',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: AppConstants.textHintColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showEditCategoryDialog(category),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                TextButton.icon(
                  onPressed: () => _deleteCategory(category),
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('Delete'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppConstants.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryStatusChip(CategoryModel category) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: category.isActive
            ? AppConstants.successColor.withOpacity(0.1)
            : AppConstants.textSecondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(
          color: category.isActive
              ? AppConstants.successColor.withOpacity(0.3)
              : AppConstants.textSecondaryColor.withOpacity(0.3),
        ),
      ),
      child: Text(
        category.isActive ? 'Active' : 'Inactive',
        style: TextStyle(
          color: category.isActive
              ? AppConstants.successColor
              : AppConstants.textSecondaryColor,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getIconFromName(String iconName) {
    switch (iconName) {
      case 'category': return Icons.category;
      case 'shopping_bag': return Icons.shopping_bag;
      case 'devices': return Icons.devices;
      case 'home': return Icons.home;
      case 'sports': return Icons.sports;
      case 'book': return Icons.book;
      case 'car': return Icons.directions_car;
      case 'restaurant': return Icons.restaurant;
      case 'phone_android': return Icons.phone_android;
      case 'computer': return Icons.computer;
      case 'watch': return Icons.watch;
      case 'headphones': return Icons.headphones;
      case 'camera': return Icons.camera_alt;
      case 'tv': return Icons.tv;
      case 'tablet': return Icons.tablet;
      case 'kitchen': return Icons.kitchen;
      case 'bed': return Icons.bed;
      case 'chair': return Icons.chair;
      case 'lamp': return Icons.lightbulb;
      case 'garden': return Icons.local_florist;
      case 'tools': return Icons.build;
      case 'paint': return Icons.palette;
      case 'cleaning': return Icons.cleaning_services;
      case 'fitness': return Icons.fitness_center;
      case 'outdoor': return Icons.nature;
      case 'games': return Icons.games;
      case 'music': return Icons.music_note;
      case 'art': return Icons.brush;
      case 'baby': return Icons.child_care;
      case 'pet': return Icons.pets;
      case 'health': return Icons.health_and_safety;
      case 'beauty': return Icons.face;
      case 'jewelry': return Icons.diamond;
      case 'clothing': return Icons.checkroom;
      case 'shoes': return Icons.directions_walk;
      case 'bags': return Icons.work;
      case 'accessories': return Icons.watch;
      default: return Icons.category;
    }
  }

  Future<void> _showAddCategoryDialog() async {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String? selectedIcon;
    bool isActive = true;
    XFile? selectedImage;

    // List of available icons
    final List<String> availableIcons = [
      'category', 'shopping_bag', 'devices', 'home', 'sports', 'book', 'car', 'restaurant',
      'phone_android', 'computer', 'watch', 'headphones', 'camera', 'tv', 'tablet',
      'kitchen', 'bed', 'chair', 'lamp', 'garden', 'tools', 'paint', 'cleaning',
      'fitness', 'outdoor', 'games', 'music', 'art', 'baby', 'pet', 'health',
      'beauty', 'jewelry', 'clothing', 'shoes', 'bags', 'accessories'
    ];
    String? uploadedImageUrl;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add New Category'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Category Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                DropdownButtonFormField<String>(
                  value: selectedIcon,
                  decoration: const InputDecoration(
                    labelText: 'Icon (Optional)',
                    border: OutlineInputBorder(),
                    hintText: 'Select an icon',
                  ),
                  items: availableIcons.map((icon) => DropdownMenuItem(
                    value: icon,
                    child: Text(_getIconDisplayName(icon)),
                  )).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedIcon = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                // Custom Icon Upload Section
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppConstants.borderColor),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Custom Icon (Optional)',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      if (selectedImage != null) ...[
                        Container(
                          height: 80,
                          width: 80,
                          decoration: BoxDecoration(
                            border: Border.all(color: AppConstants.borderColor),
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                            child: FutureBuilder<Uint8List>(
                              future: selectedImage!.readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Image.memory(
                                    snapshot.data!,
                                    height: 80,
                                    width: 80,
                                    fit: BoxFit.cover,
                                  );
                                }
                                return const Icon(
                                  Icons.image,
                                  size: 40,
                                  color: AppConstants.primaryColor,
                                );
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingSmall),
                        Text(
                          'Selected: ${selectedImage!.name}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingSmall),
                      ],
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                final image = await ImageService.pickSingleImage();
                                if (image != null) {
                                  setState(() {
                                    selectedImage = image;
                                  });
                                }
                              },
                              icon: const Icon(Icons.upload),
                              label: Text(selectedImage == null ? 'Upload Icon' : 'Change Icon'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppConstants.infoColor,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          if (selectedImage != null) ...[
                            const SizedBox(width: AppConstants.paddingSmall),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  selectedImage = null;
                                });
                              },
                              icon: const Icon(Icons.delete),
                              color: AppConstants.errorColor,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                SwitchListTile(
                  title: const Text('Active'),
                  value: isActive,
                  onChanged: (value) {
                    setState(() {
                      isActive = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isNotEmpty) {
                  String? imageUrl;

                  // Upload image if selected
                  if (selectedImage != null) {
                    try {
                      // Show loading
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const AlertDialog(
                          content: Row(
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(width: AppConstants.paddingMedium),
                              Text('Uploading image...'),
                            ],
                          ),
                        ),
                      );

                      imageUrl = await ImageService.uploadCategoryIcon(
                        imageFile: selectedImage!,
                        categoryId: DateTime.now().millisecondsSinceEpoch.toString(),
                      );

                      // Close loading dialog
                      if (Navigator.canPop(context)) {
                        Navigator.of(context).pop();
                      }

                      if (imageUrl == null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Failed to upload image. Please check your internet connection and try again.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }
                    } catch (e) {
                      // Close loading dialog if still open
                      if (Navigator.canPop(context)) {
                        Navigator.of(context).pop();
                      }

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error uploading image: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                  }

                  Navigator.of(context).pop({
                    'name': nameController.text.trim(),
                    'description': descriptionController.text.trim(),
                    'icon': selectedIcon ?? 'category',
                    'isActive': isActive,
                    'imageUrl': imageUrl,
                  });
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      await _addCategory(result);
    }
  }

  Future<void> _showEditCategoryDialog(CategoryModel category) async {
    final TextEditingController nameController = TextEditingController(text: category.name);
    final TextEditingController descriptionController = TextEditingController(text: category.description);

    // List of available icons
    final List<String> availableIcons = [
      'category', 'shopping_bag', 'devices', 'home', 'sports', 'book', 'car', 'restaurant',
      'phone_android', 'computer', 'watch', 'headphones', 'camera', 'tv', 'tablet',
      'kitchen', 'bed', 'chair', 'lamp', 'garden', 'tools', 'paint', 'cleaning',
      'fitness', 'outdoor', 'games', 'music', 'art', 'baby', 'pet', 'health',
      'beauty', 'jewelry', 'clothing', 'shoes', 'bags', 'accessories'
    ];

    // Ensure the current icon is in the list, or default to 'category'
    String selectedIcon = category.iconName ?? 'category';
    if (!availableIcons.contains(selectedIcon)) {
      selectedIcon = 'category';
    }

    bool isActive = category.isActive;
    XFile? selectedImage;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Edit Category'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Category Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                DropdownButtonFormField<String>(
                  value: selectedIcon,
                  decoration: const InputDecoration(
                    labelText: 'Icon',
                    border: OutlineInputBorder(),
                  ),
                  items: availableIcons.map((icon) => DropdownMenuItem(
                    value: icon,
                    child: Text(_getIconDisplayName(icon)),
                  )).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedIcon = value!;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.paddingMedium),

                // Image selection section
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          final image = await ImageService.pickSingleImage();
                          if (image != null) {
                            setState(() {
                              selectedImage = image;
                            });
                          }
                        },
                        icon: const Icon(Icons.image),
                        label: Text(selectedImage != null ? 'Change Image' : 'Select Image'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),

                // Show selected image preview
                if (selectedImage != null) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Container(
                    height: 100,
                    width: 100,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppConstants.borderColor),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      child: kIsWeb
                          ? FutureBuilder<Uint8List>(
                              future: selectedImage!.readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Image.memory(
                                    snapshot.data!,
                                    fit: BoxFit.cover,
                                  );
                                }
                                return const Center(child: CircularProgressIndicator());
                              },
                            )
                          : Image.file(
                              File(selectedImage!.path),
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                ] else if (category.imageUrl != null && category.imageUrl!.isNotEmpty) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Container(
                    height: 100,
                    width: 100,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppConstants.borderColor),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      child: Image.network(
                        category.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.error);
                        },
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: AppConstants.paddingMedium),
                SwitchListTile(
                  title: const Text('Active'),
                  value: isActive,
                  onChanged: (value) {
                    setState(() {
                      isActive = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isNotEmpty) {
                  String? imageUrl = category.imageUrl; // Keep existing image URL

                  // Upload new image if selected
                  if (selectedImage != null) {
                    try {
                      // Show loading
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const AlertDialog(
                          content: Row(
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(width: AppConstants.paddingMedium),
                              Text('Uploading image...'),
                            ],
                          ),
                        ),
                      );

                      imageUrl = await ImageService.uploadCategoryIcon(
                        imageFile: selectedImage!,
                        categoryId: category.id,
                      );

                      // Close loading dialog
                      if (Navigator.canPop(context)) {
                        Navigator.of(context).pop();
                      }

                      if (imageUrl == null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Failed to upload image. Please check your internet connection and try again.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }
                    } catch (e) {
                      // Close loading dialog if still open
                      if (Navigator.canPop(context)) {
                        Navigator.of(context).pop();
                      }

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error uploading image: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                  }

                  Navigator.of(context).pop({
                    'name': nameController.text.trim(),
                    'description': descriptionController.text.trim(),
                    'icon': selectedIcon,
                    'isActive': isActive,
                    'imageUrl': imageUrl,
                  });
                }
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      await _updateCategory(category, result);
    }
  }

  Future<void> _addCategory(Map<String, dynamic> data) async {
    try {
      final category = CategoryModel(
        id: CategoryService.generateCategoryId(),
        name: data['name'],
        description: data['description'],
        imageUrl: data['imageUrl'],
        iconName: data['icon'],
        isActive: data['isActive'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await CategoryService.addCategory(category);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Category added successfully')),
        );
        _loadCategories();
        _loadStatistics();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to add category')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding category: $e')),
        );
      }
    }
  }

  Future<void> _updateCategory(CategoryModel category, Map<String, dynamic> data) async {
    try {
      final updatedCategory = category.copyWith(
        name: data['name'],
        description: data['description'],
        iconName: data['icon'],
        isActive: data['isActive'],
        imageUrl: data['imageUrl'], // Update image URL
        updatedAt: DateTime.now(),
      );

      final success = await CategoryService.updateCategory(updatedCategory);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Category updated successfully')),
        );
        _loadCategories();
        _loadStatistics();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update category')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating category: $e')),
        );
      }
    }
  }

  Future<void> _deleteCategory(CategoryModel category) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete "${category.name}"?'),
            if (category.hasSubcategories) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: AppConstants.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  border: Border.all(color: AppConstants.warningColor.withOpacity(0.3)),
                ),
                child: const Text(
                  'Warning: This category has subcategories. Please delete all subcategories first.',
                  style: TextStyle(
                    color: AppConstants.warningColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: category.hasSubcategories
                ? null
                : () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await CategoryService.permanentlyDeleteCategory(category.id);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Category deleted successfully')),
          );
          _loadCategories();
          _loadStatistics();
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to delete category')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting category: $e')),
          );
        }
      }
    }
  }

  Future<void> _importExistingCategories() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Existing Categories'),
        content: const Text(
          'This will import all categories from existing products. '
          'Categories that already exist will not be duplicated. '
          'Do you want to continue?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Import'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: AppConstants.paddingMedium),
                Text('Importing categories...'),
              ],
            ),
          ),
        );

        final success = await CategoryService.importCategoriesFromProducts();

        // Close loading dialog
        if (mounted) Navigator.of(context).pop();

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Categories imported successfully')),
          );
          _loadCategories();
          _loadStatistics();
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to import categories')),
          );
        }
      } catch (e) {
        // Close loading dialog
        if (mounted) Navigator.of(context).pop();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error importing categories: $e')),
          );
        }
      }
    }
  }
}
