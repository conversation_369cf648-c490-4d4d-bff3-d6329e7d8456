import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';
import '../models/comment_model.dart';

class CommentService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = AppConstants.commentsCollection;
  static const Uuid _uuid = Uuid();

  /// Get all comments with pagination for admin
  static Future<List<CommentModel>> getAllCommentsPaginated({
    DocumentSnapshot? lastDocument,
    int limit = 20,
    String? status, // 'all', 'active', 'inactive', 'pending'
    String? postId,
    String? userId,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply status filter
      if (status != null && status != 'all') {
        switch (status) {
          case 'active':
            query = query.where('isActive', isEqualTo: true);
            break;
          case 'inactive':
            query = query.where('isActive', isEqualTo: false);
            break;
          case 'pending':
            query = query.where('isApproved', isEqualTo: false);
            break;
        }
      }

      // Apply post filter
      if (postId != null && postId.isNotEmpty) {
        query = query.where('postId', isEqualTo: postId);
      }

      // Apply user filter
      if (userId != null && userId.isNotEmpty) {
        query = query.where('userId', isEqualTo: userId);
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      List<CommentModel> comments = querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();

      // Apply search filter if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        comments = comments.where((comment) {
          return comment.content.toLowerCase().contains(searchLower) ||
                 comment.userDisplayName.toLowerCase().contains(searchLower) ||
                 comment.username.toLowerCase().contains(searchLower);
        }).toList();
      }

      return comments;
    } catch (e) {
      print('Error fetching comments: $e');
      return [];
    }
  }

  /// Get comment by ID
  static Future<CommentModel?> getCommentById(String commentId) async {
    try {
      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(commentId)
          .get();

      if (docSnapshot.exists) {
        return CommentModel.fromDocument(docSnapshot);
      }
      return null;
    } catch (e) {
      print('Error fetching comment by ID: $e');
      return null;
    }
  }

  /// Update comment status (approve/reject)
  static Future<bool> updateCommentStatus({
    required String commentId,
    required bool isApproved,
    required bool isActive,
    String? moderatorNote,
  }) async {
    try {
      final updateData = {
        'isApproved': isApproved,
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (moderatorNote != null) {
        updateData['moderatorNote'] = moderatorNote;
      }

      await _firestore
          .collection(_collection)
          .doc(commentId)
          .update(updateData);

      return true;
    } catch (e) {
      print('Error updating comment status: $e');
      return false;
    }
  }

  /// Delete comment (soft delete)
  static Future<bool> deleteComment(String commentId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(commentId)
          .update({
        'isActive': false,
        'isDeleted': true,
        'deletedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error deleting comment: $e');
      return false;
    }
  }



  /// Permanently delete comment
  static Future<bool> permanentlyDeleteComment(String commentId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(commentId)
          .delete();

      return true;
    } catch (e) {
      print('Error permanently deleting comment: $e');
      return false;
    }
  }

  /// Get comments statistics
  static Future<Map<String, int>> getCommentsStatistics() async {
    try {
      final allCommentsSnapshot = await _firestore
          .collection(_collection)
          .get();

      final activeCommentsSnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final pendingCommentsSnapshot = await _firestore
          .collection(_collection)
          .where('isApproved', isEqualTo: false)
          .where('isActive', isEqualTo: true)
          .get();

      return {
        'total': allCommentsSnapshot.docs.length,
        'active': activeCommentsSnapshot.docs.length,
        'pending': pendingCommentsSnapshot.docs.length,
        'inactive': allCommentsSnapshot.docs.length - activeCommentsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error fetching comments statistics: $e');
      return {
        'total': 0,
        'active': 0,
        'pending': 0,
        'inactive': 0,
      };
    }
  }

  /// Get comments by post ID
  static Future<List<CommentModel>> getCommentsByPostId(String postId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('postId', isEqualTo: postId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching post comments: $e');
      return [];
    }
  }

  /// Get comments by user ID
  static Future<List<CommentModel>> getCommentsByUserId(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching user comments: $e');
      return [];
    }
  }

  /// Bulk update comments
  static Future<bool> bulkUpdateComments({
    required List<String> commentIds,
    required Map<String, dynamic> updateData,
  }) async {
    try {
      final batch = _firestore.batch();

      for (String commentId in commentIds) {
        final docRef = _firestore.collection(_collection).doc(commentId);
        batch.update(docRef, {
          ...updateData,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error bulk updating comments: $e');
      return false;
    }
  }

  /// Search comments
  static Future<List<CommentModel>> searchComments({
    required String query,
    int limit = 50,
  }) async {
    try {
      if (query.isEmpty) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(limit * 2) // Get more to filter
          .get();

      final allComments = querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();

      // Filter comments based on search query
      final searchLower = query.toLowerCase();
      return allComments.where((comment) {
        return comment.content.toLowerCase().contains(searchLower) ||
               comment.userDisplayName.toLowerCase().contains(searchLower) ||
               comment.username.toLowerCase().contains(searchLower);
      }).take(limit).toList();
    } catch (e) {
      print('Error searching comments: $e');
      return [];
    }
  }

  /// Get recent comments stream for real-time updates
  static Stream<List<CommentModel>> getRecentCommentsStream({int limit = 10}) {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();
    });
  }

  /// Get comment statistics by post
  static Future<Map<String, int>> getCommentStatisticsByPost(String postId) async {
    try {
      final allCommentsSnapshot = await _firestore
          .collection(_collection)
          .where('postId', isEqualTo: postId)
          .get();

      final activeCommentsSnapshot = await _firestore
          .collection(_collection)
          .where('postId', isEqualTo: postId)
          .where('isActive', isEqualTo: true)
          .get();

      return {
        'total': allCommentsSnapshot.docs.length,
        'active': activeCommentsSnapshot.docs.length,
        'inactive': allCommentsSnapshot.docs.length - activeCommentsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error fetching comment statistics by post: $e');
      return {
        'total': 0,
        'active': 0,
        'inactive': 0,
      };
    }
  }
}
