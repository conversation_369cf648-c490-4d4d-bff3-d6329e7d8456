import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';
import '../models/post_model.dart';
import '../models/user_model.dart';

class PostService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = AppConstants.postsCollection;
  static const Uuid _uuid = Uuid();

  /// Get all posts with pagination for admin
  static Future<List<PostModel>> getAllPostsPaginated({
    DocumentSnapshot? lastDocument,
    int limit = 20,
    String? status, // 'all', 'active', 'inactive', 'pending'
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply status filter
      if (status != null && status != 'all') {
        switch (status) {
          case 'active':
            query = query.where('isActive', isEqualTo: true);
            break;
          case 'inactive':
            query = query.where('isActive', isEqualTo: false);
            break;
          case 'pending':
            query = query.where('isApproved', isEqualTo: false);
            break;
        }
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      List<PostModel> posts = querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();

      // Apply search filter if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        posts = posts.where((post) {
          return post.content.toLowerCase().contains(searchLower) ||
                 post.userDisplayName.toLowerCase().contains(searchLower) ||
                 post.username.toLowerCase().contains(searchLower);
        }).toList();
      }

      return posts;
    } catch (e) {
      print('Error fetching posts: $e');
      return [];
    }
  }

  /// Get post by ID
  static Future<PostModel?> getPostById(String postId) async {
    try {
      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(postId)
          .get();

      if (docSnapshot.exists) {
        return PostModel.fromDocument(docSnapshot);
      }
      return null;
    } catch (e) {
      print('Error fetching post by ID: $e');
      return null;
    }
  }

  /// Update post status (approve/reject)
  static Future<bool> updatePostStatus({
    required String postId,
    required bool isApproved,
    required bool isActive,
    String? moderatorNote,
  }) async {
    try {
      final updateData = {
        'isApproved': isApproved,
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (moderatorNote != null) {
        updateData['moderatorNote'] = moderatorNote;
      }

      await _firestore
          .collection(_collection)
          .doc(postId)
          .update(updateData);

      return true;
    } catch (e) {
      print('Error updating post status: $e');
      return false;
    }
  }

  /// Delete post (soft delete)
  static Future<bool> deletePost(String postId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(postId)
          .update({
        'isActive': false,
        'isDeleted': true,
        'deletedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error deleting post: $e');
      return false;
    }
  }

  /// Restore post (undo soft delete)
  static Future<bool> restorePost(String postId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(postId)
          .update({
        'isActive': true,
        'isDeleted': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error restoring post: $e');
      return false;
    }
  }

  /// Permanently delete post
  static Future<bool> permanentlyDeletePost(String postId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(postId)
          .delete();

      return true;
    } catch (e) {
      print('Error permanently deleting post: $e');
      return false;
    }
  }

  /// Get posts statistics
  static Future<Map<String, int>> getPostsStatistics() async {
    try {
      final allPostsSnapshot = await _firestore
          .collection(_collection)
          .get();

      final activePostsSnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final pendingPostsSnapshot = await _firestore
          .collection(_collection)
          .where('isApproved', isEqualTo: false)
          .where('isActive', isEqualTo: true)
          .get();

      return {
        'total': allPostsSnapshot.docs.length,
        'active': activePostsSnapshot.docs.length,
        'pending': pendingPostsSnapshot.docs.length,
        'inactive': allPostsSnapshot.docs.length - activePostsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error fetching posts statistics: $e');
      return {
        'total': 0,
        'active': 0,
        'pending': 0,
        'inactive': 0,
      };
    }
  }

  /// Bulk update posts
  static Future<bool> bulkUpdatePosts({
    required List<String> postIds,
    required Map<String, dynamic> updateData,
  }) async {
    try {
      final batch = _firestore.batch();

      for (String postId in postIds) {
        final docRef = _firestore.collection(_collection).doc(postId);
        batch.update(docRef, {
          ...updateData,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error bulk updating posts: $e');
      return false;
    }
  }

  /// Search posts
  static Future<List<PostModel>> searchPosts({
    required String query,
    int limit = 50,
  }) async {
    try {
      if (query.isEmpty) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(limit * 2) // Get more to filter
          .get();

      final allPosts = querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();

      // Filter posts based on search query
      final searchLower = query.toLowerCase();
      return allPosts.where((post) {
        return post.content.toLowerCase().contains(searchLower) ||
               post.userDisplayName.toLowerCase().contains(searchLower) ||
               post.username.toLowerCase().contains(searchLower);
      }).take(limit).toList();
    } catch (e) {
      print('Error searching posts: $e');
      return [];
    }
  }

  /// Get posts by user ID
  static Future<List<PostModel>> getPostsByUserId(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching user posts: $e');
      return [];
    }
  }

  /// Get recent posts stream for real-time updates
  static Stream<List<PostModel>> getRecentPostsStream({int limit = 10}) {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();
    });
  }
}
