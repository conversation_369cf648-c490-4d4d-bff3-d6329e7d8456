import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user_model.dart';
import 'fcm_v1_service.dart';

class PushNotificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // FCM Server Key - Retrieved from SharedPreferences (configured in FCM Settings)
  static Future<String?> _getFCMServerKey() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('fcm_server_key');
    } catch (e) {
      print('Error getting FCM server key: $e');
      return null;
    }
  }

  // Check if FCM v1 is enabled
  static Future<bool> _isFCMv1Enabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('fcm_v1_enabled') ?? false;
    } catch (e) {
      print('Error checking FCM v1 status: $e');
      return false;
    }
  }

  // Enable/Disable FCM v1
  static Future<void> setFCMv1Enabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('fcm_v1_enabled', enabled);
    } catch (e) {
      print('Error setting FCM v1 status: $e');
    }
  }

  static const String _fcmUrl = 'https://fcm.googleapis.com/fcm/send';

  /// Get user counts by type
  static Future<Map<String, int>> getUserCounts() async {
    try {
      final usersSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'user')
          .get();

      final resellersSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'reseller')
          .get();

      final adminsSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin')
          .get();

      return {
        'users': usersSnapshot.docs.length,
        'resellers': resellersSnapshot.docs.length,
        'admins': adminsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error getting user counts: $e');
      return {
        'users': 0,
        'resellers': 0,
        'admins': 0,
      };
    }
  }

  /// Get FCM tokens for specific user type
  static Future<List<String>> getFCMTokens(String userType) async {
    try {
      Query query = _firestore.collection('users');

      switch (userType) {
        case 'users':
          query = query.where('role', isEqualTo: 'user');
          break;
        case 'resellers':
          query = query.where('role', isEqualTo: 'reseller');
          break;
        case 'admins':
          query = query.where('role', isEqualTo: 'admin');
          break;
        case 'all':
        default:
          // No additional filter for all users
          break;
      }

      final snapshot = await query.get();
      final tokens = <String>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final fcmToken = data['fcmToken'] as String?;
        if (fcmToken != null && fcmToken.isNotEmpty) {
          tokens.add(fcmToken);
        }
      }

      return tokens;
    } catch (e) {
      print('Error getting FCM tokens: $e');
      return [];
    }
  }

  /// Send notification to multiple tokens
  static Future<bool> sendNotificationToTokens({
    required List<String> tokens,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    if (tokens.isEmpty) {
      print('No FCM tokens provided');
      return false;
    }

    try {
      // Split tokens into batches of 1000 (FCM limit)
      const batchSize = 1000;
      bool allSuccessful = true;

      for (int i = 0; i < tokens.length; i += batchSize) {
        final batch = tokens.skip(i).take(batchSize).toList();
        final success = await _sendBatchNotification(
          tokens: batch,
          title: title,
          message: message,
          data: data,
        );
        if (!success) {
          allSuccessful = false;
        }
      }

      return allSuccessful;
    } catch (e) {
      print('Error sending notifications: $e');
      return false;
    }
  }

  /// Send notification to a batch of tokens
  static Future<bool> _sendBatchNotification({
    required List<String> tokens,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Get the FCM server key from SharedPreferences
      final serverKey = await _getFCMServerKey();
      if (serverKey == null || serverKey.isEmpty) {
        print('FCM Server Key not configured. Please set it in FCM Settings.');
        return false;
      }

      final payload = {
        'registration_ids': tokens,
        'notification': {
          'title': title,
          'body': message,
          'sound': 'default',
          'badge': '1',
        },
        'data': data ?? {},
        'priority': 'high',
      };

      final response = await http.post(
        Uri.parse(_fcmUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=$serverKey',
        },
        body: json.encode(payload),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        print('FCM Response: $responseData');
        return responseData['success'] > 0;
      } else {
        print('FCM Error: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error sending batch notification: $e');
      return false;
    }
  }

  /// Send bulk notification to users by type
  static Future<bool> sendBulkNotification({
    required String title,
    required String message,
    required String userType,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Check if FCM v1 is enabled
      final useFCMv1 = await _isFCMv1Enabled();

      if (useFCMv1) {
        print('Using FCM API v1');
        return await FCMv1Service.sendBulkNotificationToUserType(
          title: title,
          body: message,
          userType: userType,
          data: data?.map((key, value) => MapEntry(key, value.toString())),
        );
      } else {
        print('Using FCM Legacy API');
        // Get FCM tokens for the specified user type
        final tokens = await getFCMTokens(userType);

        if (tokens.isEmpty) {
          print('No FCM tokens found for user type: $userType');
          return false;
        }

        print('Sending notification to ${tokens.length} users');

        // Send notifications using legacy API
        final success = await sendNotificationToTokens(
          tokens: tokens,
          title: title,
          message: message,
          data: data,
        );

        // Save notification to database for record keeping
        if (success) {
          await _saveNotificationRecord(
            title: title,
            message: message,
            userType: userType,
            recipientCount: tokens.length,
          );
        }

        return success;
      }
    } catch (e) {
      print('Error sending bulk notification: $e');
      return false;
    }
  }

  /// Save notification record to database
  static Future<void> _saveNotificationRecord({
    required String title,
    required String message,
    required String userType,
    required int recipientCount,
  }) async {
    try {
      await _firestore.collection('admin_notifications').add({
        'title': title,
        'message': message,
        'userType': userType,
        'recipientCount': recipientCount,
        'sentAt': FieldValue.serverTimestamp(),
        'sentBy': 'admin', // You can get actual admin ID from auth
      });
    } catch (e) {
      print('Error saving notification record: $e');
    }
  }

  /// Get notification history
  static Future<List<Map<String, dynamic>>> getNotificationHistory({
    int limit = 50,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('admin_notifications')
          .orderBy('sentAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting notification history: $e');
      return [];
    }
  }

  /// Send notification to specific user
  static Future<bool> sendNotificationToUser({
    required String userId,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        print('User not found: $userId');
        return false;
      }

      final userData = userDoc.data() as Map<String, dynamic>;
      final fcmToken = userData['fcmToken'] as String?;

      if (fcmToken == null || fcmToken.isEmpty) {
        print('No FCM token found for user: $userId');
        return false;
      }

      // Check if FCM v1 is enabled
      final useFCMv1 = await _isFCMv1Enabled();

      if (useFCMv1) {
        return await FCMv1Service.sendNotificationV1(
          token: fcmToken,
          title: title,
          body: message,
          data: data?.map((key, value) => MapEntry(key, value.toString())),
        );
      } else {
        return await sendNotificationToTokens(
          tokens: [fcmToken],
          title: title,
          message: message,
          data: data,
        );
      }
    } catch (e) {
      print('Error sending notification to user: $e');
      return false;
    }
  }

  /// Test notification (for debugging)
  static Future<bool> sendTestNotification() async {
    return await sendBulkNotification(
      title: 'Test Notification',
      message: 'This is a test notification from admin panel.',
      userType: 'all',
      data: {'type': 'test'},
    );
  }
}
